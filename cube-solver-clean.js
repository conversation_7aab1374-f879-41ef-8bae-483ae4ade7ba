/**
 * Clean Rubik's Cube Implementation with Proper Kociemba Integration
 * This implementation ensures valid cube states and proper solving
 */

class CleanCube {
    constructor() {
        this.reset();
    }

    reset() {
        // Initialize solved cube in Kociemba format directly
        // U=0, R=1, F=2, D=3, L=4, B=5
        this.state = [
            // U face (0-8)
            0, 0, 0, 0, 0, 0, 0, 0, 0,
            // R face (9-17)  
            1, 1, 1, 1, 1, 1, 1, 1, 1,
            // F face (18-26)
            2, 2, 2, 2, 2, 2, 2, 2, 2,
            // D face (27-35)
            3, 3, 3, 3, 3, 3, 3, 3, 3,
            // L face (36-44)
            4, 4, 4, 4, 4, 4, 4, 4, 4,
            // B face (45-53)
            5, 5, 5, 5, 5, 5, 5, 5, 5
        ];
    }

    // Convert to Kociemba string format
    getKociembaString() {
        const faceChars = ['U', 'R', 'F', 'D', 'L', 'B'];
        return this.state.map(face => faceChars[face]).join('');
    }

    // Convert to display format (WRGYOR)
    getDisplayString() {
        const colorChars = ['W', 'R', 'G', 'Y', 'O', 'B'];
        return this.state.map(face => colorChars[face]).join('');
    }

    // Check if cube is solved
    isSolved() {
        // Check each face has uniform color
        for (let face = 0; face < 6; face++) {
            const start = face * 9;
            const faceColor = this.state[start];
            for (let i = 1; i < 9; i++) {
                if (this.state[start + i] !== faceColor) {
                    return false;
                }
            }
        }
        return true;
    }

    // Rotate a face 90 degrees clockwise
    rotateFace(face) {
        const start = face * 9;
        const temp = [...this.state];
        
        // Rotate face clockwise: corners and edges
        this.state[start + 0] = temp[start + 6]; // 0 ← 6
        this.state[start + 1] = temp[start + 3]; // 1 ← 3
        this.state[start + 2] = temp[start + 0]; // 2 ← 0
        this.state[start + 3] = temp[start + 7]; // 3 ← 7
        this.state[start + 4] = temp[start + 4]; // 4 ← 4 (center)
        this.state[start + 5] = temp[start + 1]; // 5 ← 1
        this.state[start + 6] = temp[start + 8]; // 6 ← 8
        this.state[start + 7] = temp[start + 5]; // 7 ← 5
        this.state[start + 8] = temp[start + 2]; // 8 ← 2
    }

    // Execute basic moves using proven algorithms
    executeMove(move) {
        const temp = [...this.state];
        
        switch (move) {
            case 'U':
                this.rotateFace(0); // Rotate U face
                // Cycle edges: F-top → L-top → B-top → R-top → F-top
                [this.state[18], this.state[19], this.state[20]] = [temp[36], temp[37], temp[38]]; // F ← L
                [this.state[36], this.state[37], this.state[38]] = [temp[45], temp[46], temp[47]]; // L ← B
                [this.state[45], this.state[46], this.state[47]] = [temp[9], temp[10], temp[11]];   // B ← R
                [this.state[9], this.state[10], this.state[11]] = [temp[18], temp[19], temp[20]];   // R ← F
                break;

            case 'D':
                this.rotateFace(3); // Rotate D face
                // Cycle edges: F-bottom → R-bottom → B-bottom → L-bottom → F-bottom
                [this.state[24], this.state[25], this.state[26]] = [temp[15], temp[16], temp[17]]; // F ← R
                [this.state[15], this.state[16], this.state[17]] = [temp[51], temp[52], temp[53]]; // R ← B
                [this.state[51], this.state[52], this.state[53]] = [temp[42], temp[43], temp[44]]; // B ← L
                [this.state[42], this.state[43], this.state[44]] = [temp[24], temp[25], temp[26]]; // L ← F
                break;

            case 'R':
                this.rotateFace(1); // Rotate R face
                // Cycle edges: U-right → F-right → D-right → B-left(reversed) → U-right
                [this.state[2], this.state[5], this.state[8]] = [temp[20], temp[23], temp[26]];     // U ← F
                [this.state[20], this.state[23], this.state[26]] = [temp[29], temp[32], temp[35]]; // F ← D
                [this.state[29], this.state[32], this.state[35]] = [temp[51], temp[48], temp[45]]; // D ← B(rev)
                [this.state[51], this.state[48], this.state[45]] = [temp[2], temp[5], temp[8]];    // B(rev) ← U
                break;

            case 'L':
                this.rotateFace(4); // Rotate L face
                // Cycle edges: U-left → B-right(reversed) → D-left → F-left → U-left
                [this.state[0], this.state[3], this.state[6]] = [temp[47], temp[50], temp[53]];     // U ← B(rev)
                [this.state[47], this.state[50], this.state[53]] = [temp[33], temp[30], temp[27]]; // B(rev) ← D
                [this.state[33], this.state[30], this.state[27]] = [temp[24], temp[21], temp[18]]; // D ← F
                [this.state[24], this.state[21], this.state[18]] = [temp[0], temp[3], temp[6]];    // F ← U
                break;

            case 'F':
                this.rotateFace(2); // Rotate F face
                // Cycle edges: U-bottom → R-left → D-top(reversed) → L-right(reversed) → U-bottom
                [this.state[6], this.state[7], this.state[8]] = [temp[44], temp[41], temp[38]];     // U ← L(rev)
                [this.state[44], this.state[41], this.state[38]] = [temp[33], temp[34], temp[35]]; // L(rev) ← D(rev)
                [this.state[33], this.state[34], this.state[35]] = [temp[15], temp[12], temp[9]];  // D(rev) ← R(rev)
                [this.state[15], this.state[12], this.state[9]] = [temp[6], temp[7], temp[8]];     // R(rev) ← U
                break;

            case 'B':
                this.rotateFace(5); // Rotate B face
                // Cycle edges: U-top(reversed) → L-left → D-bottom → R-right(reversed) → U-top
                [this.state[2], this.state[1], this.state[0]] = [temp[36], temp[39], temp[42]];     // U(rev) ← L
                [this.state[36], this.state[39], this.state[42]] = [temp[27], temp[28], temp[29]]; // L ← D
                [this.state[27], this.state[28], this.state[29]] = [temp[17], temp[14], temp[11]]; // D ← R(rev)
                [this.state[17], this.state[14], this.state[11]] = [temp[2], temp[1], temp[0]];    // R(rev) ← U(rev)
                break;

            default:
                console.warn(`Unknown move: ${move}`);
        }
    }

    // Apply a sequence of moves
    applyMoves(moveString) {
        const moves = moveString.split(' ').filter(m => m.trim());
        
        for (const move of moves) {
            const trimmed = move.trim();
            if (!trimmed) continue;
            
            const face = trimmed[0];
            const modifier = trimmed.slice(1);
            
            if (modifier === "'") {
                // Prime move: apply 3 times
                this.executeMove(face);
                this.executeMove(face);
                this.executeMove(face);
            } else if (modifier === "2") {
                // Double move: apply 2 times
                this.executeMove(face);
                this.executeMove(face);
            } else {
                // Normal move: apply 1 time
                this.executeMove(face);
            }
        }
    }

    // Generate random scramble
    generateScramble(length = 20) {
        const faces = ['U', 'D', 'R', 'L', 'F', 'B'];
        const modifiers = ['', "'", '2'];
        const moves = [];
        let lastFace = '';

        for (let i = 0; i < length; i++) {
            let face;
            do {
                face = faces[Math.floor(Math.random() * faces.length)];
            } while (face === lastFace);
            
            const modifier = modifiers[Math.floor(Math.random() * modifiers.length)];
            moves.push(face + modifier);
            lastFace = face;
        }

        return moves.join(' ');
    }

    // Validate cube state
    isValidState() {
        // Count each face color
        const counts = [0, 0, 0, 0, 0, 0];
        for (const face of this.state) {
            if (face < 0 || face > 5) return false;
            counts[face]++;
        }
        
        // Each face should appear exactly 9 times
        return counts.every(count => count === 9);
    }
}

// Test the implementation
function testCleanCube() {
    console.log('=== Testing Clean Cube Implementation ===');
    
    const cube = new CleanCube();
    console.log('Initial state:', cube.getKociembaString());
    console.log('Is solved:', cube.isSolved());
    console.log('Is valid:', cube.isValidState());
    
    // Test R2 move
    cube.applyMoves('R2');
    console.log('After R2:', cube.getKociembaString());
    console.log('Is solved:', cube.isSolved());
    console.log('Is valid:', cube.isValidState());
    
    // Test R2 again (should return to solved)
    cube.applyMoves('R2');
    console.log('After R2 R2:', cube.getKociembaString());
    console.log('Is solved:', cube.isSolved());
    console.log('Is valid:', cube.isValidState());
    
    return cube.isSolved() && cube.isValidState();
}

// Export for browser use
if (typeof window !== 'undefined') {
    window.CleanCube = CleanCube;
    window.testCleanCube = testCleanCube;
} else {
    module.exports = { CleanCube, testCleanCube };
}

// Auto-test in Node.js
if (typeof window === 'undefined') {
    const success = testCleanCube();
    console.log('Test result:', success ? 'PASSED' : 'FAILED');
}
