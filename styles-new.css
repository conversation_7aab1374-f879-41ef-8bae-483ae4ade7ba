/* Complete Rubik's Cube Solver - Enhanced Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.2em;
    color: #666;
    margin-bottom: 20px;
}

.cube-size-selector {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.size-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    background: #f8f9fa;
    color: #333;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.size-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.size-btn.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

/* Main Layout */
main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

@media (max-width: 1200px) {
    main {
        grid-template-columns: 1fr;
    }
}

/* Cube Container */
.cube-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    grid-column: 1 / -1;
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    .cube-container {
        grid-template-columns: 1fr;
    }
}

.view-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.view-section h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.3em;
}

#cube-canvas {
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.orientation-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.control-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    background: #667eea;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.control-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* Flat Cube */
.flat-cube {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    max-width: 300px;
    margin: 0 auto 20px;
}

.cube-face {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2px;
    padding: 5px;
    border-radius: 8px;
    background: #333;
}

.cube-sticker {
    aspect-ratio: 1;
    border-radius: 3px;
    border: 1px solid #333;
}

/* Colors */
.white { background-color: #ffffff; }
.yellow { background-color: #ffeb3b; }
.green { background-color: #4caf50; }
.blue { background-color: #2196f3; }
.red { background-color: #f44336; }
.orange { background-color: #ff9800; }

/* Learning Section */
.learning-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    grid-column: 1 / -1;
}

.learning-tabs {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    background: #f8f9fa;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: #667eea;
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.notation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.notation-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.move {
    font-weight: bold;
    font-size: 1.2em;
    color: #667eea;
    min-width: 30px;
}

.algorithm-item, .method-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 15px;
}

.try-btn {
    padding: 5px 15px;
    border: none;
    border-radius: 15px;
    background: #4ecdc4;
    color: white;
    cursor: pointer;
    margin-left: 10px;
    transition: all 0.3s ease;
}

.try-btn:hover {
    background: #45b7d1;
}

/* Controls Section */
.controls-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.face-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    max-width: 400px;
    margin: 0 auto;
}

.face-row {
    display: contents;
}

.face-btn {
    padding: 15px;
    border: none;
    border-radius: 10px;
    background: #667eea;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1em;
}

.face-btn:hover {
    background: #5a67d8;
    transform: translateY(-2px);
}

.face-btn:active {
    transform: translateY(0);
}

/* Action Controls */
.action-controls {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    text-align: center;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1em;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.action-btn.primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    color: white;
}

.action-btn.success {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(45deg, #95a5a6, #7f8c8d);
    color: white;
}

.action-btn.info {
    background: linear-gradient(45deg, #45b7d1, #3498db);
    color: white;
}

/* Manual Input */
.manual-input {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    grid-column: 1 / -1;
}

.input-group {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

#manual-moves-input {
    flex: 1;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1em;
    min-width: 300px;
}

#manual-moves-input:focus {
    outline: none;
    border-color: #667eea;
}

/* Status Section */
.status-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    grid-column: 1 / -1;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.status-item {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.status-item h4 {
    margin-bottom: 15px;
    color: #333;
}

.status-content {
    font-family: 'Courier New', monospace;
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    min-height: 60px;
    word-break: break-all;
}

/* Color Reference */
.color-reference {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    grid-column: 1 / -1;
}

.color-mappings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.color-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.color-box {
    width: 30px;
    height: 30px;
    border-radius: 5px;
    border: 2px solid #333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .face-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    #manual-moves-input {
        min-width: auto;
    }
}
