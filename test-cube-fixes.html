<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cube Fixes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background: #2a2a2a;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background: #4caf50;
            color: white;
        }
        .error {
            background: #f44336;
            color: white;
        }
        .warning {
            background: #ff9800;
            color: white;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #1976d2;
        }
        pre {
            background: #333;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Cube Rotation Fixes Test</h1>
    
    <div class="test-section">
        <h2>Basic Move Tests</h2>
        <button onclick="testBasicMoves()">Test Basic Moves</button>
        <button onclick="testEdgeRotations()">Test Edge Rotations</button>
        <button onclick="testMultipleMoves()">Test Multiple Moves</button>
        <div id="basic-test-results"></div>
    </div>

    <div class="test-section">
        <h2>Validation Tests</h2>
        <button onclick="testValidation()">Test Cube Validation</button>
        <button onclick="testCenterPreservation()">Test Center Preservation</button>
        <div id="validation-test-results"></div>
    </div>

    <div class="test-section">
        <h2>Cube State Display</h2>
        <button onclick="showCubeState()">Show Current State</button>
        <button onclick="resetCube()">Reset to Solved</button>
        <div id="cube-state-display"></div>
    </div>

    <script src="js/cube-state.js"></script>
    <script>
        let testCube = new CubeState(3);
        
        function log(message, type = 'info') {
            console.log(message);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            return resultDiv;
        }

        function testBasicMoves() {
            const resultsDiv = document.getElementById('basic-test-results');
            resultsDiv.innerHTML = '';
            
            try {
                testCube.resetToSolved();
                const initialState = testCube.cubeString;
                resultsDiv.appendChild(log('✓ Reset to solved state', 'success'));
                
                // Test each face move
                const faces = ['U', 'D', 'F', 'B', 'R', 'L'];
                for (const face of faces) {
                    try {
                        testCube.executeMove(face, true);
                        testCube.executeMove(face, false);
                        
                        if (testCube.cubeString === initialState) {
                            resultsDiv.appendChild(log(`✓ ${face} move and inverse work correctly`, 'success'));
                        } else {
                            resultsDiv.appendChild(log(`✗ ${face} move failed - state not restored`, 'error'));
                        }
                    } catch (error) {
                        resultsDiv.appendChild(log(`✗ ${face} move failed: ${error.message}`, 'error'));
                    }
                }
                
            } catch (error) {
                resultsDiv.appendChild(log(`✗ Basic move test failed: ${error.message}`, 'error'));
            }
        }

        function testEdgeRotations() {
            const resultsDiv = document.getElementById('basic-test-results');
            
            try {
                testCube.resetToSolved();
                
                // Test that 4 identical moves return to original state
                const testMoves = ['U', 'R', 'F'];
                for (const face of testMoves) {
                    const initialState = testCube.cubeString;
                    
                    // Execute move 4 times
                    for (let i = 0; i < 4; i++) {
                        testCube.executeMove(face, true);
                    }
                    
                    if (testCube.cubeString === initialState) {
                        resultsDiv.appendChild(log(`✓ Four ${face} moves return to original state`, 'success'));
                    } else {
                        resultsDiv.appendChild(log(`✗ Four ${face} moves failed to return to original state`, 'error'));
                    }
                }
                
            } catch (error) {
                resultsDiv.appendChild(log(`✗ Edge rotation test failed: ${error.message}`, 'error'));
            }
        }

        function testMultipleMoves() {
            const resultsDiv = document.getElementById('basic-test-results');
            
            try {
                testCube.resetToSolved();
                
                // Test sequence that should return to solved state
                const sequence = ['R', 'U', 'R\'', 'U\''];
                const initialState = testCube.cubeString;
                
                // Execute sequence 6 times (should return to original)
                for (let cycle = 0; cycle < 6; cycle++) {
                    for (const move of sequence) {
                        const face = move.charAt(0);
                        const clockwise = !move.includes('\'');
                        testCube.executeMove(face, clockwise);
                    }
                }
                
                if (testCube.cubeString === initialState) {
                    resultsDiv.appendChild(log('✓ Complex move sequence works correctly', 'success'));
                } else {
                    resultsDiv.appendChild(log('✗ Complex move sequence failed', 'error'));
                }
                
            } catch (error) {
                resultsDiv.appendChild(log(`✗ Multiple moves test failed: ${error.message}`, 'error'));
            }
        }

        function testValidation() {
            const resultsDiv = document.getElementById('validation-test-results');
            resultsDiv.innerHTML = '';
            
            try {
                testCube.resetToSolved();
                
                if (testCube.validateCubeState()) {
                    resultsDiv.appendChild(log('✓ Solved cube passes validation', 'success'));
                } else {
                    resultsDiv.appendChild(log('✗ Solved cube fails validation', 'error'));
                }
                
                // Test after some moves
                testCube.executeMove('R', true);
                testCube.executeMove('U', true);
                testCube.executeMove('R', false);
                
                if (testCube.validateCubeState()) {
                    resultsDiv.appendChild(log('✓ Scrambled cube passes validation', 'success'));
                } else {
                    resultsDiv.appendChild(log('✗ Scrambled cube fails validation', 'error'));
                }
                
            } catch (error) {
                resultsDiv.appendChild(log(`✗ Validation test failed: ${error.message}`, 'error'));
            }
        }

        function testCenterPreservation() {
            const resultsDiv = document.getElementById('validation-test-results');
            
            try {
                testCube.resetToSolved();
                const faces = testCube.stringToFaces();
                const originalCenters = {
                    U: faces.U[1][1],
                    D: faces.D[1][1],
                    F: faces.F[1][1],
                    B: faces.B[1][1],
                    R: faces.R[1][1],
                    L: faces.L[1][1]
                };
                
                // Execute random moves
                const moves = ['R', 'U', 'F', 'L', 'D', 'B'];
                for (let i = 0; i < 20; i++) {
                    const randomFace = moves[Math.floor(Math.random() * moves.length)];
                    const randomDirection = Math.random() > 0.5;
                    testCube.executeMove(randomFace, randomDirection);
                }
                
                const newFaces = testCube.stringToFaces();
                let centersPreserved = true;
                
                for (const [face, originalCenter] of Object.entries(originalCenters)) {
                    if (newFaces[face][1][1] !== originalCenter) {
                        centersPreserved = false;
                        break;
                    }
                }
                
                if (centersPreserved) {
                    resultsDiv.appendChild(log('✓ Centers preserved after random moves', 'success'));
                } else {
                    resultsDiv.appendChild(log('✗ Centers not preserved after random moves', 'error'));
                }
                
            } catch (error) {
                resultsDiv.appendChild(log(`✗ Center preservation test failed: ${error.message}`, 'error'));
            }
        }

        function showCubeState() {
            const displayDiv = document.getElementById('cube-state-display');
            const faces = testCube.stringToFaces();
            
            let html = '<h3>Current Cube State:</h3>';
            html += '<pre>';
            
            for (const [faceName, face] of Object.entries(faces)) {
                html += `${faceName} Face:\n`;
                for (let row = 0; row < 3; row++) {
                    html += face[row].join(' ') + '\n';
                }
                html += '\n';
            }
            
            html += '</pre>';
            html += `<p>Cube String: <code>${testCube.cubeString}</code></p>`;
            html += `<p>Is Solved: ${testCube.isSolved() ? 'Yes' : 'No'}</p>`;
            html += `<p>Is Valid: ${testCube.validateCubeState() ? 'Yes' : 'No'}</p>`;
            
            displayDiv.innerHTML = html;
        }

        function resetCube() {
            testCube.resetToSolved();
            showCubeState();
            document.getElementById('cube-state-display').appendChild(log('✓ Cube reset to solved state', 'success'));
        }

        // Initialize display
        showCubeState();
    </script>
</body>
</html>
