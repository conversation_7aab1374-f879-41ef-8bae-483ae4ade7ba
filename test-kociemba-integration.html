<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kociemba Integration Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: 0;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-section h2 {
            color: #ffeb3b;
            margin-top: 0;
        }
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        button {
            background: linear-gradient(145deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .output {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #4CAF50; }
        .status-offline { background: #f44336; }
        .cube-display {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧩 Kociemba Integration Test Suite</h1>
        
        <div class="test-section">
            <h2>🔍 Server Status</h2>
            <div class="controls">
                <button onclick="checkServerStatus()">Check Server Status</button>
                <button onclick="startServerInstructions()">Show Server Setup</button>
            </div>
            <div id="server-status" class="output">Click "Check Server Status" to test connection...</div>
        </div>

        <div class="test-section">
            <h2>🎲 Cube Conversion Test</h2>
            <div class="controls">
                <button onclick="testCubeConversion()">Test Cube Format Conversion</button>
                <button onclick="testFaceCube()">Test FaceCube Class</button>
            </div>
            <div id="conversion-output" class="output">Ready to test cube format conversion...</div>
        </div>

        <div class="test-section">
            <h2>🧠 Solving Test</h2>
            <div class="controls">
                <button onclick="testSolvedCube()">Test Solved Cube</button>
                <button onclick="testScrambledCube()">Test Scrambled Cube</button>
                <button onclick="testCustomCube()">Test Custom Cube</button>
            </div>
            <div id="solving-output" class="output">Ready to test cube solving...</div>
        </div>

        <div class="test-section">
            <h2>🔧 Integration Test</h2>
            <div class="controls">
                <button onclick="testFullIntegration()">Test Full Integration</button>
                <button onclick="testErrorHandling()">Test Error Handling</button>
            </div>
            <div id="integration-output" class="output">Ready to test full integration...</div>
        </div>
    </div>

    <!-- Include our Kociemba modules -->
    <script src="js/kociemba-enums.js"></script>
    <script src="js/kociemba-face.js"></script>
    <script src="js/kociemba-solver.js"></script>
    <script src="js/cube-state.js"></script>

    <script>
        let solver = new KociembaSolver();
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const span = document.createElement('span');
            span.className = type;
            span.textContent = `[${timestamp}] ${message}\n`;
            element.appendChild(span);
            element.scrollTop = element.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearOutput(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        async function checkServerStatus() {
            clearOutput('server-status');
            log('server-status', '🔍 Checking server status...', 'info');
            
            try {
                const isRunning = await solver.checkServer();
                const status = solver.getServerStatus();
                
                if (isRunning) {
                    log('server-status', '✅ Kociemba server is running!', 'success');
                    log('server-status', `📡 Server URL: ${status.url}`, 'info');
                    log('server-status', `⚙️ Max Length: ${status.maxLength}`, 'info');
                    log('server-status', `⏱️ Timeout: ${status.timeout}s`, 'info');
                } else {
                    log('server-status', '❌ Kociemba server is not running', 'error');
                    log('server-status', '💡 Please start the server using: python kociemba-server.py', 'warning');
                }
                
            } catch (error) {
                log('server-status', `❌ Error checking server: ${error.message}`, 'error');
            }
        }

        function startServerInstructions() {
            clearOutput('server-status');
            log('server-status', '📋 Kociemba Server Setup Instructions:', 'info');
            log('server-status', '', 'info');
            log('server-status', '1. Ensure RubiksCube-TwophaseSolver-master folder is present', 'info');
            log('server-status', '2. Run: python kociemba-server.py 8080', 'info');
            log('server-status', '3. Wait for "Server is ready to accept requests..." message', 'info');
            log('server-status', '4. First run may take 30+ minutes to generate lookup tables', 'info');
            log('server-status', '', 'info');
            log('server-status', '💡 Alternative: Use the original solver directly:', 'info');
            log('server-status', '   cd RubiksCube-TwophaseSolver-master', 'info');
            log('server-status', '   python start_server.py 8080 20 3', 'info');
        }

        async function testCubeConversion() {
            clearOutput('conversion-output');
            log('conversion-output', '🔄 Testing cube format conversion...', 'info');
            
            try {
                // Create a test cube state
                const cubeState = new CubeState(3);
                cubeState.resetToSolved();
                
                log('conversion-output', '✅ Created solved cube state', 'success');
                
                // Test FaceCube conversion
                const faceCube = new FaceCube();
                const kociembaString = faceCube.fromOurCubeState(cubeState);
                
                if (kociembaString) {
                    log('conversion-output', `✅ Conversion successful: ${kociembaString}`, 'success');
                    log('conversion-output', `📏 String length: ${kociembaString.length}`, 'info');
                    
                    // Validate the string
                    const validation = faceCube.validate();
                    if (validation === true) {
                        log('conversion-output', '✅ Kociemba string validation passed', 'success');
                    } else {
                        log('conversion-output', `❌ Validation failed: ${validation}`, 'error');
                    }
                    
                } else {
                    log('conversion-output', '❌ Conversion failed', 'error');
                }
                
            } catch (error) {
                log('conversion-output', `❌ Conversion test failed: ${error.message}`, 'error');
            }
        }

        async function testFaceCube() {
            clearOutput('conversion-output');
            log('conversion-output', '🧪 Testing FaceCube class...', 'info');
            
            try {
                const faceCube = new FaceCube();
                
                // Test solved cube
                const solvedString = 'UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB';
                const result = faceCube.fromString(solvedString);
                
                if (result === true) {
                    log('conversion-output', '✅ FaceCube can parse solved cube', 'success');
                    
                    const backToString = faceCube.toKociembaString();
                    if (backToString === solvedString) {
                        log('conversion-output', '✅ Round-trip conversion successful', 'success');
                    } else {
                        log('conversion-output', '❌ Round-trip conversion failed', 'error');
                        log('conversion-output', `Expected: ${solvedString}`, 'info');
                        log('conversion-output', `Got:      ${backToString}`, 'info');
                    }
                    
                    // Test 2D display
                    const display2D = faceCube.to2DString();
                    log('conversion-output', '📋 2D Cube Display:', 'info');
                    log('conversion-output', display2D, 'info');
                    
                } else {
                    log('conversion-output', `❌ FaceCube parsing failed: ${result}`, 'error');
                }
                
            } catch (error) {
                log('conversion-output', `❌ FaceCube test failed: ${error.message}`, 'error');
            }
        }

        async function testSolvedCube() {
            clearOutput('solving-output');
            log('solving-output', '🧩 Testing solved cube...', 'info');
            
            try {
                const solvedString = 'UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB';
                log('solving-output', `📤 Sending: ${solvedString}`, 'info');
                
                const solution = await solver.solve(solvedString);
                log('solving-output', `📥 Received: ${solution}`, 'success');
                
                if (solution.includes('Already solved') || solution.trim() === '') {
                    log('solving-output', '✅ Correctly identified as solved', 'success');
                } else {
                    log('solving-output', '⚠️ Unexpected response for solved cube', 'warning');
                }
                
            } catch (error) {
                log('solving-output', `❌ Solved cube test failed: ${error.message}`, 'error');
            }
        }

        async function testScrambledCube() {
            clearOutput('solving-output');
            log('solving-output', '🎲 Testing scrambled cube...', 'info');
            
            try {
                // Create a scrambled cube
                const cubeState = new CubeState(3);
                cubeState.resetToSolved();
                
                // Apply some moves
                const scrambleMoves = ['R', 'U', 'R\'', 'F', 'R', 'F\''];
                for (const move of scrambleMoves) {
                    const face = move.charAt(0);
                    const clockwise = !move.includes('\'');
                    cubeState.executeMove(face, clockwise);
                }
                
                log('solving-output', `🔀 Applied scramble: ${scrambleMoves.join(' ')}`, 'info');
                
                // Convert to Kociemba format
                const faceCube = new FaceCube();
                const kociembaString = faceCube.fromOurCubeState(cubeState);
                
                if (kociembaString) {
                    log('solving-output', `📤 Sending: ${kociembaString}`, 'info');
                    
                    const solution = await solver.solve(kociembaString);
                    log('solving-output', `📥 Solution: ${solution}`, 'success');
                    
                    if (solution && !solution.includes('Error')) {
                        const moves = solver.parseMoves(solution);
                        log('solving-output', `📊 Solution length: ${moves.length} moves`, 'info');
                        log('solving-output', `🎯 Parsed moves: ${moves.join(' ')}`, 'info');
                    }
                    
                } else {
                    log('solving-output', '❌ Failed to convert scrambled cube', 'error');
                }
                
            } catch (error) {
                log('solving-output', `❌ Scrambled cube test failed: ${error.message}`, 'error');
            }
        }

        async function testCustomCube() {
            clearOutput('solving-output');
            log('solving-output', '🎨 Testing custom cube configuration...', 'info');
            
            // A known solvable cube state (R U R' U' applied to solved cube)
            const customString = 'UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB';
            
            try {
                log('solving-output', `📤 Testing custom cube: ${customString}`, 'info');
                
                const solution = await solver.solve(customString);
                log('solving-output', `📥 Solution: ${solution}`, 'success');
                
            } catch (error) {
                log('solving-output', `❌ Custom cube test failed: ${error.message}`, 'error');
            }
        }

        async function testFullIntegration() {
            clearOutput('integration-output');
            log('integration-output', '🔧 Testing full integration...', 'info');
            
            try {
                // Test the complete workflow
                const cubeState = new CubeState(3);
                cubeState.resetToSolved();
                
                // Apply a known sequence
                const moves = ['R', 'U', 'R\'', 'U\''];
                for (const move of moves) {
                    const face = move.charAt(0);
                    const clockwise = !move.includes('\'');
                    cubeState.executeMove(face, clockwise);
                }
                
                log('integration-output', `🔀 Applied moves: ${moves.join(' ')}`, 'info');
                
                // Use the solver's high-level interface
                const result = await solver.solveOurCube(cubeState);
                
                if (result.success) {
                    log('integration-output', '✅ Full integration successful!', 'success');
                    log('integration-output', `🎯 Solution: ${result.solution}`, 'success');
                    log('integration-output', `📊 Move count: ${result.moves.length}`, 'info');
                    log('integration-output', `🔧 Kociemba string: ${result.kociembaString}`, 'info');
                } else {
                    log('integration-output', `❌ Integration failed: ${result.error}`, 'error');
                }
                
            } catch (error) {
                log('integration-output', `❌ Full integration test failed: ${error.message}`, 'error');
            }
        }

        async function testErrorHandling() {
            clearOutput('integration-output');
            log('integration-output', '🛡️ Testing error handling...', 'info');
            
            try {
                // Test invalid cube string
                log('integration-output', '🧪 Testing invalid cube string...', 'info');
                const invalidString = 'INVALID_CUBE_STRING';
                
                try {
                    const result = await solver.solve(invalidString);
                    log('integration-output', `📥 Response: ${result}`, 'info');
                    
                    if (result.includes('Error') || result.includes('error')) {
                        log('integration-output', '✅ Error correctly handled', 'success');
                    } else {
                        log('integration-output', '⚠️ Expected error response', 'warning');
                    }
                } catch (error) {
                    log('integration-output', `✅ Exception correctly thrown: ${error.message}`, 'success');
                }
                
                // Test server timeout
                log('integration-output', '🧪 Testing server availability...', 'info');
                const serverAvailable = await solver.checkServer();
                
                if (!serverAvailable) {
                    log('integration-output', '✅ Server unavailability correctly detected', 'success');
                } else {
                    log('integration-output', '✅ Server is available', 'success');
                }
                
            } catch (error) {
                log('integration-output', `❌ Error handling test failed: ${error.message}`, 'error');
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            log('server-status', '🚀 Kociemba Integration Test Suite loaded', 'success');
            log('server-status', '💡 Click "Check Server Status" to begin testing', 'info');
        });
    </script>
</body>
</html>
