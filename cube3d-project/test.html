<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cube Test Suite</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #4CAF50;
            margin-bottom: 30px;
        }
        .test-section {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-output {
            background: #111;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #1976d2;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196f3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 3x3 Cube Test Suite</h1>
        
        <div class="test-section">
            <h2>🔧 Basic Functionality Tests</h2>
            <button onclick="testCubeState()">Test Cube State</button>
            <button onclick="testMoves()">Test Face Moves</button>
            <button onclick="testScramble()">Test Scrambling</button>
            <div id="basic-output" class="test-output">Click buttons to run tests...</div>
        </div>

        <div class="test-section">
            <h2>🌐 Server Connection Tests</h2>
            <button onclick="testServerConnection()">Test Server Connection</button>
            <button onclick="testSolving()">Test Solving</button>
            <div id="server-output" class="test-output">Click buttons to test server...</div>
        </div>

        <div class="test-section">
            <h2>🎲 Integration Tests</h2>
            <button onclick="testFullWorkflow()">Test Full Workflow</button>
            <button onclick="runAllTests()">Run All Tests</button>
            <div id="integration-output" class="test-output">Click buttons to run integration tests...</div>
        </div>
    </div>

    <script src="js/cube-state.js"></script>
    <script src="js/kociemba-solver.js"></script>
    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const span = document.createElement('span');
            span.className = type;
            span.textContent = `[${timestamp}] ${message}\n`;
            element.appendChild(span);
            element.scrollTop = element.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearOutput(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        function testCubeState() {
            clearOutput('basic-output');
            log('basic-output', '🧪 Testing CubeState class...', 'info');
            
            try {
                // Test initialization
                const cube = new CubeState();
                log('basic-output', '✅ CubeState created successfully', 'success');
                
                // Test solved state
                if (cube.isSolved()) {
                    log('basic-output', '✅ Initial state is solved', 'success');
                } else {
                    log('basic-output', '❌ Initial state is not solved', 'error');
                }
                
                // Test cube string
                const cubeString = cube.toString();
                log('basic-output', `📋 Cube string: ${cubeString}`, 'info');
                
                if (cubeString.length === 54) {
                    log('basic-output', '✅ Cube string has correct length', 'success');
                } else {
                    log('basic-output', `❌ Cube string has wrong length: ${cubeString.length}`, 'error');
                }
                
                // Test validation
                if (cube.validate()) {
                    log('basic-output', '✅ Cube state validation passed', 'success');
                } else {
                    log('basic-output', '❌ Cube state validation failed', 'error');
                }
                
                // Test faces
                const faces = cube.getFaces();
                log('basic-output', `📋 Faces: ${Object.keys(faces).join(', ')}`, 'info');
                
            } catch (error) {
                log('basic-output', `❌ CubeState test failed: ${error.message}`, 'error');
            }
        }

        function testMoves() {
            clearOutput('basic-output');
            log('basic-output', '🔄 Testing face moves...', 'info');
            
            try {
                const cube = new CubeState();
                const initialState = cube.toString();
                
                // Test each face
                const faces = ['U', 'R', 'F', 'D', 'L', 'B'];
                
                for (const face of faces) {
                    cube.reset();
                    
                    // Test clockwise and counter-clockwise
                    cube.executeMove(face, true);
                    const afterClockwise = cube.toString();
                    
                    cube.executeMove(face, false);
                    const afterCounterClockwise = cube.toString();
                    
                    if (afterCounterClockwise === initialState) {
                        log('basic-output', `✅ ${face} move works correctly`, 'success');
                    } else {
                        log('basic-output', `❌ ${face} move failed`, 'error');
                    }
                }
                
                // Test 4-move cycle
                cube.reset();
                for (let i = 0; i < 4; i++) {
                    cube.executeMove('R', true);
                }
                
                if (cube.toString() === initialState) {
                    log('basic-output', '✅ 4-move cycle works correctly', 'success');
                } else {
                    log('basic-output', '❌ 4-move cycle failed', 'error');
                }
                
            } catch (error) {
                log('basic-output', `❌ Move test failed: ${error.message}`, 'error');
            }
        }

        function testScramble() {
            clearOutput('basic-output');
            log('basic-output', '🎲 Testing scrambling...', 'info');
            
            try {
                const cube = new CubeState();
                const initialState = cube.toString();
                
                // Test scramble
                const scrambleSequence = cube.scramble(10);
                log('basic-output', `🔀 Scramble: ${scrambleSequence}`, 'info');
                
                const scrambledState = cube.toString();
                
                if (scrambledState !== initialState) {
                    log('basic-output', '✅ Scrambling changed cube state', 'success');
                } else {
                    log('basic-output', '⚠️ Scrambling did not change cube state', 'warning');
                }
                
                if (cube.validate()) {
                    log('basic-output', '✅ Scrambled cube is still valid', 'success');
                } else {
                    log('basic-output', '❌ Scrambled cube is invalid', 'error');
                }
                
                // Test move history
                const history = cube.getMoveHistory();
                log('basic-output', `📋 Move history length: ${history.length}`, 'info');
                
            } catch (error) {
                log('basic-output', `❌ Scramble test failed: ${error.message}`, 'error');
            }
        }

        async function testServerConnection() {
            clearOutput('server-output');
            log('server-output', '🌐 Testing server connection...', 'info');
            
            try {
                const solver = new KociembaSolver();
                
                // Test server availability
                const isAvailable = await solver.checkServer();
                
                if (isAvailable) {
                    log('server-output', '✅ Kociemba server is available', 'success');
                    
                    const status = solver.getServerStatus();
                    log('server-output', `📋 Server URL: ${status.url}`, 'info');
                    log('server-output', `📋 Max length: ${status.maxLength}`, 'info');
                    log('server-output', `📋 Timeout: ${status.timeout}s`, 'info');
                    
                } else {
                    log('server-output', '❌ Kociemba server is not available', 'error');
                    log('server-output', '💡 Start server with: python kociemba-server.py 8080', 'warning');
                }
                
            } catch (error) {
                log('server-output', `❌ Server connection test failed: ${error.message}`, 'error');
            }
        }

        async function testSolving() {
            clearOutput('server-output');
            log('server-output', '🧠 Testing solving...', 'info');
            
            try {
                const solver = new KociembaSolver();
                const cube = new CubeState();
                
                // Test solved cube
                log('server-output', '🧪 Testing solved cube...', 'info');
                let result = await solver.solveCube(cube);
                
                if (result.success) {
                    log('server-output', `✅ Solved cube result: ${result.solution}`, 'success');
                } else {
                    log('server-output', `❌ Solved cube test failed: ${result.error}`, 'error');
                }
                
                // Test scrambled cube
                log('server-output', '🧪 Testing scrambled cube...', 'info');
                cube.scramble(5);
                result = await solver.solveCube(cube);
                
                if (result.success) {
                    log('server-output', `✅ Scrambled cube solution: ${result.solution}`, 'success');
                    log('server-output', `📊 Solution length: ${result.moves.length} moves`, 'info');
                } else {
                    log('server-output', `❌ Scrambled cube test failed: ${result.error}`, 'error');
                }
                
            } catch (error) {
                log('server-output', `❌ Solving test failed: ${error.message}`, 'error');
            }
        }

        async function testFullWorkflow() {
            clearOutput('integration-output');
            log('integration-output', '🔧 Testing full workflow...', 'info');
            
            try {
                const cube = new CubeState();
                const solver = new KociembaSolver();
                
                // 1. Start with solved cube
                log('integration-output', '1️⃣ Starting with solved cube...', 'info');
                if (!cube.isSolved()) {
                    throw new Error('Cube should start solved');
                }
                
                // 2. Scramble
                log('integration-output', '2️⃣ Scrambling cube...', 'info');
                const scramble = cube.scramble(8);
                log('integration-output', `🔀 Scramble: ${scramble}`, 'info');
                
                // 3. Solve
                log('integration-output', '3️⃣ Solving cube...', 'info');
                const result = await solver.solveCube(cube);
                
                if (result.success) {
                    log('integration-output', `✅ Solution found: ${result.solution}`, 'success');
                    
                    // 4. Apply solution (simulate)
                    log('integration-output', '4️⃣ Applying solution...', 'info');
                    cube.applyMoveSequence(result.solution);
                    
                    // 5. Check if solved
                    if (cube.isSolved()) {
                        log('integration-output', '✅ Full workflow completed successfully!', 'success');
                    } else {
                        log('integration-output', '❌ Cube not solved after applying solution', 'error');
                    }
                    
                } else {
                    log('integration-output', `❌ Solving failed: ${result.error}`, 'error');
                }
                
            } catch (error) {
                log('integration-output', `❌ Full workflow test failed: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            clearOutput('integration-output');
            log('integration-output', '🚀 Running all tests...', 'info');
            
            // Run all tests sequentially
            testCubeState();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testMoves();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testScramble();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testServerConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testSolving();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testFullWorkflow();
            
            log('integration-output', '🏁 All tests completed!', 'success');
        }

        // Initialize
        window.addEventListener('load', () => {
            log('basic-output', '🎉 Test suite loaded successfully', 'success');
            log('basic-output', '💡 Click buttons to run individual tests', 'info');
        });
    </script>
</body>
</html>
