<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Cube Test Suite</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #4CAF50;
            margin-bottom: 30px;
        }
        .test-section {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-output {
            background: #111;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #1976d2;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196f3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Enhanced 3x3 Cube Test Suite</h1>
        
        <div class="test-section">
            <h2>🔧 Move Engine Tests</h2>
            <button onclick="testMoveEngine()">Test Move Engine</button>
            <button onclick="testPermutations()">Test Permutations</button>
            <button onclick="testUndo()">Test Undo</button>
            <div id="engine-output" class="test-output">Click buttons to test move engine...</div>
        </div>

        <div class="test-section">
            <h2>🎲 Scramble Tests</h2>
            <button onclick="testAutoScramble()">Test Auto Scramble</button>
            <button onclick="testCustomScramble()">Test Custom Scramble</button>
            <button onclick="testSequenceParsing()">Test Sequence Parsing</button>
            <div id="scramble-output" class="test-output">Click buttons to test scrambling...</div>
        </div>

        <div class="test-section">
            <h2>🌐 Server & Solving Tests</h2>
            <button onclick="testServerConnection()">Test Server Connection</button>
            <button onclick="testSolving()">Test Solving</button>
            <div id="server-output" class="test-output">Click buttons to test server...</div>
        </div>

        <div class="test-section">
            <h2>🎯 Integration Tests</h2>
            <button onclick="testFullWorkflow()">Test Full Workflow</button>
            <button onclick="runAllTests()">Run All Tests</button>
            <div id="integration-output" class="test-output">Click buttons to run integration tests...</div>
        </div>
    </div>

    <script src="js/move-engine.js"></script>
    <script src="js/cube-state.js"></script>
    <script src="js/kociemba-solver.js"></script>
    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const span = document.createElement('span');
            span.className = type;
            span.textContent = `[${timestamp}] ${message}\n`;
            element.appendChild(span);
            element.scrollTop = element.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearOutput(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        function testMoveEngine() {
            clearOutput('engine-output');
            log('engine-output', '🔧 Testing Move Engine...', 'info');
            
            try {
                const cube = new CubeState();
                log('engine-output', '✅ CubeState with MoveEngine created', 'success');
                
                if (cube.moveEngine) {
                    log('engine-output', '✅ Move engine initialized', 'success');
                    
                    // Test basic move
                    const permutation = cube.executeMove('R', true);
                    log('engine-output', `📋 R move permutation count: ${permutation ? permutation.length : 'N/A'}`, 'info');
                    
                    // Test statistics
                    const stats = cube.moveEngine.getStatistics();
                    log('engine-output', `📊 Total moves: ${stats.totalMoves}`, 'info');
                    log('engine-output', `📊 Can undo: ${stats.canUndo}`, 'info');
                    log('engine-output', `📊 Last move: ${stats.lastMove || 'None'}`, 'info');
                    
                } else {
                    log('engine-output', '❌ Move engine not initialized', 'error');
                }
                
            } catch (error) {
                log('engine-output', `❌ Move engine test failed: ${error.message}`, 'error');
            }
        }

        function testPermutations() {
            clearOutput('engine-output');
            log('engine-output', '🔄 Testing Permutation Tracking...', 'info');
            
            try {
                const cube = new CubeState();
                
                if (!cube.moveEngine) {
                    log('engine-output', '❌ Move engine not available', 'error');
                    return;
                }
                
                // Execute a sequence and track permutations
                const sequence = 'R U R\' U\'';
                log('engine-output', `🎯 Executing sequence: ${sequence}`, 'info');
                
                const permutations = cube.moveEngine.executeSequence(sequence);
                log('engine-output', `📋 Total permutations: ${permutations.length}`, 'info');
                
                // Get move history
                const history = cube.moveEngine.getMoveHistory();
                log('engine-output', `📊 Move history: ${history.moves.join(' ')}`, 'info');
                log('engine-output', `📊 Total moves executed: ${history.totalMoves}`, 'info');
                
                // Test cycle detection
                const cycles = cube.moveEngine.getPermutationCycles();
                log('engine-output', `🔍 Permutation analysis: ${JSON.stringify(cycles)}`, 'info');
                
            } catch (error) {
                log('engine-output', `❌ Permutation test failed: ${error.message}`, 'error');
            }
        }

        function testUndo() {
            clearOutput('engine-output');
            log('engine-output', '↩️ Testing Undo Functionality...', 'info');
            
            try {
                const cube = new CubeState();
                
                if (!cube.moveEngine) {
                    log('engine-output', '❌ Move engine not available', 'error');
                    return;
                }
                
                const initialState = cube.toString();
                log('engine-output', '📋 Initial state saved', 'info');
                
                // Execute some moves
                cube.executeMove('R', true);
                cube.executeMove('U', true);
                cube.executeMove('F', true);
                
                const afterMoves = cube.toString();
                log('engine-output', '📋 Executed R U F', 'info');
                
                if (afterMoves !== initialState) {
                    log('engine-output', '✅ Moves changed cube state', 'success');
                } else {
                    log('engine-output', '⚠️ Moves did not change cube state', 'warning');
                }
                
                // Test undo
                const undoResult1 = cube.moveEngine.undo();
                const undoResult2 = cube.moveEngine.undo();
                const undoResult3 = cube.moveEngine.undo();
                
                log('engine-output', `↩️ Undo results: ${undoResult1}, ${undoResult2}, ${undoResult3}`, 'info');
                
                const finalState = cube.toString();
                if (finalState === initialState) {
                    log('engine-output', '✅ Undo restored original state', 'success');
                } else {
                    log('engine-output', '❌ Undo failed to restore original state', 'error');
                }
                
                // Test undo when no moves available
                const undoEmpty = cube.moveEngine.undo();
                if (!undoEmpty) {
                    log('engine-output', '✅ Undo correctly reports no moves available', 'success');
                } else {
                    log('engine-output', '❌ Undo should report no moves available', 'error');
                }
                
            } catch (error) {
                log('engine-output', `❌ Undo test failed: ${error.message}`, 'error');
            }
        }

        function testAutoScramble() {
            clearOutput('scramble-output');
            log('scramble-output', '🎲 Testing Auto Scramble...', 'info');
            
            try {
                const cube = new CubeState();
                const initialState = cube.toString();
                
                // Test different scramble lengths
                const lengths = [5, 10, 15, 20];
                
                for (const length of lengths) {
                    cube.reset();
                    const scramble = cube.scramble(length);
                    
                    log('scramble-output', `🔀 Length ${length}: ${scramble}`, 'info');
                    
                    const moves = scramble.split(' ').filter(m => m.length > 0);
                    if (moves.length === length) {
                        log('scramble-output', `✅ Correct scramble length: ${length}`, 'success');
                    } else {
                        log('scramble-output', `❌ Wrong scramble length: expected ${length}, got ${moves.length}`, 'error');
                    }
                    
                    if (cube.toString() !== initialState) {
                        log('scramble-output', `✅ Scramble changed cube state`, 'success');
                    } else {
                        log('scramble-output', `⚠️ Scramble did not change cube state`, 'warning');
                    }
                }
                
            } catch (error) {
                log('scramble-output', `❌ Auto scramble test failed: ${error.message}`, 'error');
            }
        }

        function testCustomScramble() {
            clearOutput('scramble-output');
            log('scramble-output', '📝 Testing Custom Scramble...', 'info');
            
            try {
                const cube = new CubeState();
                
                // Test various custom sequences
                const sequences = [
                    'R U R\' U\'',
                    'F R U\' R\' F\'',
                    'R U2 R\' U\' R U\' R\'',
                    'U R U\' L\' U R\' U\' L'
                ];
                
                for (const sequence of sequences) {
                    cube.reset();
                    const initialState = cube.toString();
                    
                    log('scramble-output', `🎯 Testing sequence: ${sequence}`, 'info');
                    
                    cube.applyMoveSequence(sequence);
                    
                    if (cube.toString() !== initialState) {
                        log('scramble-output', `✅ Custom sequence applied successfully`, 'success');
                    } else {
                        log('scramble-output', `⚠️ Custom sequence did not change cube`, 'warning');
                    }
                    
                    if (cube.validate()) {
                        log('scramble-output', `✅ Cube state valid after sequence`, 'success');
                    } else {
                        log('scramble-output', `❌ Cube state invalid after sequence`, 'error');
                    }
                }
                
            } catch (error) {
                log('scramble-output', `❌ Custom scramble test failed: ${error.message}`, 'error');
            }
        }

        function testSequenceParsing() {
            clearOutput('scramble-output');
            log('scramble-output', '🔍 Testing Sequence Parsing...', 'info');
            
            try {
                const cube = new CubeState();
                
                if (!cube.moveEngine) {
                    log('scramble-output', '❌ Move engine not available', 'error');
                    return;
                }
                
                // Test various sequence formats
                const testSequences = [
                    'R U R\' U\'',
                    'R U2 R\' U\' R U\' R\'',
                    'F R U\' R\' F\'',
                    'U R U\' L\' U R\' U\' L',
                    'R2 U2 R\' U\' R U\' R2'
                ];
                
                for (const sequence of testSequences) {
                    try {
                        const moves = cube.moveEngine.parseSequence(sequence);
                        log('scramble-output', `✅ Parsed "${sequence}": ${moves.length} moves`, 'success');
                        
                        // Show parsed moves
                        const parsedMoves = moves.map(m => `${m.face}${m.clockwise ? '' : "'"}`).join(' ');
                        log('scramble-output', `   → ${parsedMoves}`, 'info');
                        
                    } catch (error) {
                        log('scramble-output', `❌ Failed to parse "${sequence}": ${error.message}`, 'error');
                    }
                }
                
            } catch (error) {
                log('scramble-output', `❌ Sequence parsing test failed: ${error.message}`, 'error');
            }
        }

        async function testServerConnection() {
            clearOutput('server-output');
            log('server-output', '🌐 Testing Server Connection...', 'info');
            
            try {
                const solver = new KociembaSolver();
                
                const isAvailable = await solver.checkServer();
                
                if (isAvailable) {
                    log('server-output', '✅ Kociemba server is available', 'success');
                    
                    const status = solver.getServerStatus();
                    log('server-output', `📋 Server URL: ${status.url}`, 'info');
                    log('server-output', `📋 Max length: ${status.maxLength}`, 'info');
                    log('server-output', `📋 Timeout: ${status.timeout}s`, 'info');
                    
                } else {
                    log('server-output', '❌ Kociemba server is not available', 'error');
                    log('server-output', '💡 Start server with: python kociemba-server.py 8080', 'warning');
                }
                
            } catch (error) {
                log('server-output', `❌ Server connection test failed: ${error.message}`, 'error');
            }
        }

        async function testSolving() {
            clearOutput('server-output');
            log('server-output', '🧠 Testing Solving...', 'info');
            
            try {
                const solver = new KociembaSolver();
                const cube = new CubeState();
                
                // Test solved cube
                log('server-output', '🧪 Testing solved cube...', 'info');
                let result = await solver.solveCube(cube);
                
                if (result.success) {
                    log('server-output', `✅ Solved cube result: ${result.solution}`, 'success');
                } else {
                    log('server-output', `❌ Solved cube test failed: ${result.error}`, 'error');
                }
                
                // Test scrambled cube
                log('server-output', '🧪 Testing scrambled cube...', 'info');
                cube.scramble(8);
                result = await solver.solveCube(cube);
                
                if (result.success) {
                    log('server-output', `✅ Scrambled cube solution: ${result.solution}`, 'success');
                    log('server-output', `📊 Solution length: ${result.moves.length} moves`, 'info');
                } else {
                    log('server-output', `❌ Scrambled cube test failed: ${result.error}`, 'error');
                }
                
            } catch (error) {
                log('server-output', `❌ Solving test failed: ${error.message}`, 'error');
            }
        }

        async function testFullWorkflow() {
            clearOutput('integration-output');
            log('integration-output', '🔧 Testing Full Enhanced Workflow...', 'info');
            
            try {
                const cube = new CubeState();
                const solver = new KociembaSolver();
                
                // 1. Start with solved cube
                log('integration-output', '1️⃣ Starting with solved cube...', 'info');
                if (!cube.isSolved()) {
                    throw new Error('Cube should start solved');
                }
                
                // 2. Test move engine
                if (cube.moveEngine) {
                    log('integration-output', '2️⃣ Testing move engine integration...', 'info');
                    cube.executeMove('R', true);
                    cube.executeMove('U', true);
                    
                    const stats = cube.moveEngine.getStatistics();
                    log('integration-output', `📊 Move engine stats: ${stats.totalMoves} moves`, 'info');
                    
                    // Test undo
                    cube.moveEngine.undo();
                    cube.moveEngine.undo();
                    
                    if (cube.isSolved()) {
                        log('integration-output', '✅ Undo functionality works', 'success');
                    } else {
                        log('integration-output', '❌ Undo functionality failed', 'error');
                    }
                }
                
                // 3. Scramble with custom sequence
                log('integration-output', '3️⃣ Applying custom scramble...', 'info');
                const customSequence = 'R U R\' U\' F R F\' U R U\' R\'';
                cube.applyMoveSequence(customSequence);
                log('integration-output', `🔀 Applied: ${customSequence}`, 'info');
                
                // 4. Solve
                log('integration-output', '4️⃣ Solving cube...', 'info');
                const result = await solver.solveCube(cube);
                
                if (result.success) {
                    log('integration-output', `✅ Solution found: ${result.solution}`, 'success');
                    
                    // 5. Apply solution
                    log('integration-output', '5️⃣ Applying solution...', 'info');
                    cube.applyMoveSequence(result.solution);
                    
                    // 6. Check if solved
                    if (cube.isSolved()) {
                        log('integration-output', '✅ Full enhanced workflow completed successfully!', 'success');
                    } else {
                        log('integration-output', '❌ Cube not solved after applying solution', 'error');
                    }
                    
                } else {
                    log('integration-output', `❌ Solving failed: ${result.error}`, 'error');
                }
                
            } catch (error) {
                log('integration-output', `❌ Full workflow test failed: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            clearOutput('integration-output');
            log('integration-output', '🚀 Running All Enhanced Tests...', 'info');
            
            // Run all tests sequentially
            testMoveEngine();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testPermutations();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testUndo();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testAutoScramble();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testCustomScramble();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testSequenceParsing();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testServerConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testSolving();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testFullWorkflow();
            
            log('integration-output', '🏁 All enhanced tests completed!', 'success');
        }

        // Initialize
        window.addEventListener('load', () => {
            log('engine-output', '🎉 Enhanced test suite loaded successfully', 'success');
            log('engine-output', '💡 Click buttons to run individual tests', 'info');
        });
    </script>
</body>
</html>
