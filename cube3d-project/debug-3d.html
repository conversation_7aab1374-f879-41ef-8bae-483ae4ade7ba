<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Cube Debug Tool</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #4CAF50;
            margin-bottom: 30px;
        }
        .debug-section {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .debug-output {
            background: #111;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #1976d2;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196f3; }
        .mapping-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
            margin: 20px 0;
        }
        .face-debug {
            background: #222;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 10px;
        }
        .face-debug h4 {
            color: #ffeb3b;
            margin-bottom: 10px;
            text-align: center;
        }
        .position-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2px;
            font-size: 10px;
        }
        .position-cell {
            background: #333;
            border: 1px solid #555;
            padding: 4px;
            text-align: center;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 3D Cube Mapping Debug Tool</h1>
        
        <div class="debug-section">
            <h2>🧪 3D Position Mapping Tests</h2>
            <button onclick="testInitialMapping()">Test Initial Mapping</button>
            <button onclick="testSingleMove()">Test Single Move</button>
            <button onclick="testMoveSequence()">Test Move Sequence</button>
            <button onclick="compareWith2D()">Compare 3D vs 2D</button>
            <div id="mapping-output" class="debug-output">Click buttons to test 3D mapping...</div>
        </div>

        <div class="debug-section">
            <h2>📋 Position Reference</h2>
            <div id="position-reference" class="mapping-grid">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>

        <div class="debug-section">
            <h2>🎯 Live Cube State</h2>
            <button onclick="showCurrentState()">Show Current State</button>
            <button onclick="applyTestMove()">Apply Test Move (R)</button>
            <button onclick="resetCube()">Reset Cube</button>
            <div id="state-output" class="debug-output">Click "Show Current State" to see live data...</div>
        </div>
    </div>

    <script src="js/move-engine.js"></script>
    <script src="js/cube-state.js"></script>
    <script>
        let testCube = new CubeState();
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const span = document.createElement('span');
            span.className = type;
            span.textContent = `[${timestamp}] ${message}\n`;
            element.appendChild(span);
            element.scrollTop = element.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearOutput(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        function testInitialMapping() {
            clearOutput('mapping-output');
            log('mapping-output', '🧪 Testing Initial 3D Mapping...', 'info');
            
            testCube.reset();
            const facelets = testCube.facelets;
            
            log('mapping-output', '📋 Kociemba String: ' + testCube.toString(), 'info');
            log('mapping-output', '', 'info');
            
            // Test each 3D position mapping
            log('mapping-output', '🔍 3D Position to Facelet Mapping:', 'info');
            
            for (let x = 0; x <= 2; x++) {
                for (let y = 0; y <= 2; y++) {
                    for (let z = 0; z <= 2; z++) {
                        const mapping = get3DMapping(x, y, z, facelets);
                        if (mapping.visible.length > 0) {
                            log('mapping-output', `Position (${x},${y},${z}): ${mapping.visible.join(', ')}`, 'success');
                        }
                    }
                }
            }
        }

        function get3DMapping(x, y, z, facelets) {
            const visible = [];
            
            // Right face (+X, x = 2)
            if (x === 2) {
                const row = 2 - y;
                const col = z;
                const index = 9 + row * 3 + col;
                visible.push(`R[${row},${col}]=${facelets[index]} (pos ${index})`);
            }
            
            // Left face (-X, x = 0)
            if (x === 0) {
                const row = 2 - y;
                const col = 2 - z;
                const index = 36 + row * 3 + col;
                visible.push(`L[${row},${col}]=${facelets[index]} (pos ${index})`);
            }
            
            // Top face (+Y, y = 2)
            if (y === 2) {
                const row = 2 - z;
                const col = x;
                const index = 0 + row * 3 + col;
                visible.push(`U[${row},${col}]=${facelets[index]} (pos ${index})`);
            }
            
            // Bottom face (-Y, y = 0)
            if (y === 0) {
                const row = z;
                const col = x;
                const index = 27 + row * 3 + col;
                visible.push(`D[${row},${col}]=${facelets[index]} (pos ${index})`);
            }
            
            // Front face (+Z, z = 2)
            if (z === 2) {
                const row = 2 - y;
                const col = x;
                const index = 18 + row * 3 + col;
                visible.push(`F[${row},${col}]=${facelets[index]} (pos ${index})`);
            }
            
            // Back face (-Z, z = 0)
            if (z === 0) {
                const row = 2 - y;
                const col = 2 - x;
                const index = 45 + row * 3 + col;
                visible.push(`B[${row},${col}]=${facelets[index]} (pos ${index})`);
            }
            
            return { visible };
        }

        function testSingleMove() {
            clearOutput('mapping-output');
            log('mapping-output', '🔄 Testing Single Move (R)...', 'info');
            
            testCube.reset();
            log('mapping-output', 'Before R move: ' + testCube.toString(), 'info');
            
            testCube.executeMove('R', true);
            log('mapping-output', 'After R move:  ' + testCube.toString(), 'info');
            
            // Check specific positions that should change
            const facelets = testCube.facelets;
            
            log('mapping-output', '', 'info');
            log('mapping-output', '🔍 Right face after R move:', 'info');
            for (let i = 0; i < 9; i++) {
                const pos = 9 + i;
                const row = Math.floor(i / 3);
                const col = i % 3;
                log('mapping-output', `R[${row},${col}] = ${facelets[pos]} (position ${pos})`, 'success');
            }
            
            // Check edge changes
            log('mapping-output', '', 'info');
            log('mapping-output', '🔍 Edge positions that should have changed:', 'info');
            const edgePositions = [2, 5, 8, 20, 23, 26, 29, 32, 35, 53, 50, 47];
            for (const pos of edgePositions) {
                log('mapping-output', `Position ${pos}: ${facelets[pos]}`, 'info');
            }
        }

        function testMoveSequence() {
            clearOutput('mapping-output');
            log('mapping-output', '🎯 Testing Move Sequence (R U R\' U\')...', 'info');
            
            testCube.reset();
            const sequence = ['R', 'U', 'R\'', 'U\''];
            
            log('mapping-output', 'Initial: ' + testCube.toString(), 'info');
            
            for (let i = 0; i < sequence.length; i++) {
                const move = sequence[i];
                const face = move[0];
                const clockwise = !move.includes('\'');
                
                testCube.executeMove(face, clockwise);
                log('mapping-output', `After ${move}: ${testCube.toString()}`, 'info');
            }
            
            if (testCube.isSolved()) {
                log('mapping-output', '✅ Sequence returned to solved state!', 'success');
            } else {
                log('mapping-output', '❌ Sequence did not return to solved state', 'error');
            }
        }

        function compareWith2D() {
            clearOutput('mapping-output');
            log('mapping-output', '🔄 Comparing 3D vs 2D representation...', 'info');
            
            testCube.reset();
            testCube.executeMove('R', true);
            
            const faces = testCube.getFaces();
            const facelets = testCube.facelets;
            
            log('mapping-output', '📋 2D Face Arrays:', 'info');
            const faceNames = ['U', 'R', 'F', 'D', 'L', 'B'];
            
            for (const faceName of faceNames) {
                const face = faces[faceName];
                log('mapping-output', `${faceName} face:`, 'info');
                for (let row = 0; row < 3; row++) {
                    let rowStr = '  ';
                    for (let col = 0; col < 3; col++) {
                        rowStr += face[row][col] + ' ';
                    }
                    log('mapping-output', rowStr, 'info');
                }
                log('mapping-output', '', 'info');
            }
            
            log('mapping-output', '📋 3D Position Mapping Check:', 'info');
            // Check a few key 3D positions
            const testPositions = [
                {x: 2, y: 2, z: 0, desc: 'Top-Right-Back corner'},
                {x: 2, y: 1, z: 1, desc: 'Right face center'},
                {x: 1, y: 2, z: 2, desc: 'Top-Front edge'},
            ];
            
            for (const pos of testPositions) {
                const mapping = get3DMapping(pos.x, pos.y, pos.z, facelets);
                log('mapping-output', `${pos.desc} (${pos.x},${pos.y},${pos.z}): ${mapping.visible.join(', ')}`, 'success');
            }
        }

        function showCurrentState() {
            clearOutput('state-output');
            log('state-output', '📊 Current Cube State:', 'info');
            
            const facelets = testCube.facelets;
            const faces = testCube.getFaces();
            
            log('state-output', 'Kociemba String: ' + testCube.toString(), 'info');
            log('state-output', 'Is Solved: ' + testCube.isSolved(), 'info');
            log('state-output', '', 'info');
            
            // Show face arrays
            const faceNames = ['U', 'R', 'F', 'D', 'L', 'B'];
            for (const faceName of faceNames) {
                const face = faces[faceName];
                log('state-output', `${faceName} face:`, 'info');
                for (let row = 0; row < 3; row++) {
                    let rowStr = '  ';
                    for (let col = 0; col < 3; col++) {
                        rowStr += face[row][col] + ' ';
                    }
                    log('state-output', rowStr, 'info');
                }
                log('state-output', '', 'info');
            }
            
            // Show move engine stats if available
            if (testCube.moveEngine) {
                const stats = testCube.moveEngine.getStatistics();
                log('state-output', '🔧 Move Engine Stats:', 'info');
                log('state-output', `Total moves: ${stats.totalMoves}`, 'info');
                log('state-output', `Can undo: ${stats.canUndo}`, 'info');
                log('state-output', `Last move: ${stats.lastMove || 'None'}`, 'info');
            }
        }

        function applyTestMove() {
            log('state-output', '🔄 Applying test move (R)...', 'info');
            testCube.executeMove('R', true);
            showCurrentState();
        }

        function resetCube() {
            log('state-output', '🔄 Resetting cube...', 'info');
            testCube.reset();
            showCurrentState();
        }

        function generatePositionReference() {
            const container = document.getElementById('position-reference');
            const faceNames = ['U', 'R', 'F', 'D', 'L', 'B'];
            const faceColors = {
                'U': '#ffffff', 'R': '#ff4444', 'F': '#44ff44',
                'D': '#ffff44', 'L': '#ff8844', 'B': '#4444ff'
            };
            
            container.innerHTML = '';
            
            for (let f = 0; f < 6; f++) {
                const faceName = faceNames[f];
                const startPos = f * 9;
                const color = faceColors[faceName];
                
                const faceDiv = document.createElement('div');
                faceDiv.className = 'face-debug';
                faceDiv.innerHTML = `<h4 style="color: ${color};">${faceName} (${startPos}-${startPos + 8})</h4>`;
                
                const grid = document.createElement('div');
                grid.className = 'position-grid';
                
                for (let i = 0; i < 9; i++) {
                    const pos = startPos + i;
                    const row = Math.floor(i / 3);
                    const col = i % 3;
                    
                    const cell = document.createElement('div');
                    cell.className = 'position-cell';
                    cell.innerHTML = `${pos}<br>[${row},${col}]`;
                    cell.style.backgroundColor = color;
                    cell.style.color = faceName === 'D' ? '#000' : '#fff';
                    
                    grid.appendChild(cell);
                }
                
                faceDiv.appendChild(grid);
                container.appendChild(faceDiv);
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            generatePositionReference();
            log('mapping-output', '🎉 3D Debug tool loaded successfully', 'success');
            log('mapping-output', '💡 Use buttons to test 3D position mapping', 'info');
        });
    </script>
</body>
</html>
