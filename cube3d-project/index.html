<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3x3 Rubik's Cube Solver</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .cube-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .cube-section h2 {
            text-align: center;
            margin-bottom: 20px;
            color: #ffeb3b;
            font-size: 1.5em;
        }

        #cube3d-container {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.2);
        }

        #cube2d-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 10px;
            max-width: 300px;
            margin: 0 auto;
        }

        .face {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 2px;
            padding: 8px;
            background: #333;
            border-radius: 8px;
            aspect-ratio: 1;
            position: relative;
            border: 2px solid #555;
        }

        .face-label {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            color: #ffeb3b;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            z-index: 10;
        }

        .sticker {
            border-radius: 4px;
            border: 2px solid #000;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .sticker:hover {
            transform: scale(1.1);
            z-index: 10;
        }

        /* Face positioning */
        .face-U { grid-column: 2; grid-row: 1; }
        .face-L { grid-column: 1; grid-row: 2; }
        .face-F { grid-column: 2; grid-row: 2; }
        .face-R { grid-column: 3; grid-row: 2; }
        .face-D { grid-column: 2; grid-row: 3; }
        .face-B { grid-column: 2; grid-row: 4; }

        /* Colors */
        .color-white { background: #ffffff; }
        .color-yellow { background: #ffff00; }
        .color-red { background: #ff0000; }
        .color-orange { background: #ff8000; }
        .color-green { background: #00ff00; }
        .color-blue { background: #0000ff; }

        .controls-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 20px;
        }

        .controls-section h3 {
            color: #ffeb3b;
            margin-bottom: 15px;
            text-align: center;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group h4 {
            margin-bottom: 10px;
            color: #4CAF50;
        }

        .face-controls {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .face-btn {
            background: linear-gradient(145deg, #2196F3, #1976D2);
            color: white;
            border: none;
            padding: 12px 8px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .face-btn:hover {
            background: linear-gradient(145deg, #1976D2, #1565C0);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .face-btn:active {
            transform: translateY(0);
        }

        .action-controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .action-btn {
            background: linear-gradient(145deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .action-btn:hover {
            background: linear-gradient(145deg, #45a049, #388e3c);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .action-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .scramble-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .scramble-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .scramble-row label {
            font-weight: bold;
            color: #4CAF50;
            min-width: 60px;
        }

        .scramble-row input[type="text"] {
            flex: 1;
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #ccc;
            font-family: 'Courier New', monospace;
        }

        .scramble-row input[type="number"] {
            width: 60px;
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ccc;
            text-align: center;
        }

        .cube-state-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 20px;
        }

        .cube-state-section h3 {
            color: #ffeb3b;
            margin-bottom: 15px;
            text-align: center;
        }

        .state-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .state-tab {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .state-tab:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .state-tab.active {
            background: linear-gradient(145deg, #4CAF50, #45a049);
            border-color: #4CAF50;
        }

        .state-content {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
            max-height: 400px;
            overflow-y: auto;
        }

        .kociemba-string {
            word-break: break-all;
            font-size: 14px;
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #333;
        }

        .face-array {
            margin: 15px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }

        .face-array h4 {
            color: #4CAF50;
            margin-bottom: 10px;
        }

        .face-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            max-width: 150px;
        }

        .face-cell {
            background: rgba(0, 0, 0, 0.3);
            padding: 8px;
            text-align: center;
            border-radius: 4px;
            border: 1px solid #555;
            font-weight: bold;
        }

        .position-map {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
            font-size: 12px;
        }

        .position-group {
            background: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #333;
        }

        .position-group h5 {
            color: #ffeb3b;
            margin-bottom: 8px;
            text-align: center;
        }

        .solution-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .solution-section h3 {
            color: #ffeb3b;
            margin-bottom: 15px;
            text-align: center;
        }

        #solution-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
        }

        .status-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .status-item {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .status-item h4 {
            color: #4CAF50;
            margin-bottom: 5px;
        }

        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 1000;
            max-width: 350px;
            font-weight: 500;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            animation: slideIn 0.3s ease-out;
        }

        .message.success {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }

        .message.error {
            background: linear-gradient(45deg, #f44336, #d32f2f);
            color: white;
        }

        .message.warning {
            background: linear-gradient(45deg, #ff9800, #f57c00);
            color: white;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .face-controls {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .action-controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧩 3x3 Rubik's Cube Solver</h1>
            <p>Interactive 3D cube with Kociemba two-phase algorithm</p>
        </div>

        <div class="main-content">
            <div class="cube-section">
                <h2>🎲 3D View</h2>
                <div id="cube3d-container"></div>
            </div>

            <div class="cube-section">
                <h2>📋 2D View</h2>
                <div id="cube2d-container"></div>
            </div>
        </div>

        <div class="controls-section">
            <h3>🎮 Cube Controls</h3>
            
            <div class="control-group">
                <h4>Face Rotations</h4>
                <div class="face-controls">
                    <button class="face-btn" onclick="app.rotateFace('U', true)">U</button>
                    <button class="face-btn" onclick="app.rotateFace('R', true)">R</button>
                    <button class="face-btn" onclick="app.rotateFace('F', true)">F</button>
                    <button class="face-btn" onclick="app.rotateFace('D', true)">D</button>
                    <button class="face-btn" onclick="app.rotateFace('L', true)">L</button>
                    <button class="face-btn" onclick="app.rotateFace('B', true)">B</button>
                    <button class="face-btn" onclick="app.rotateFace('U', false)">U'</button>
                    <button class="face-btn" onclick="app.rotateFace('R', false)">R'</button>
                    <button class="face-btn" onclick="app.rotateFace('F', false)">F'</button>
                    <button class="face-btn" onclick="app.rotateFace('D', false)">D'</button>
                    <button class="face-btn" onclick="app.rotateFace('L', false)">L'</button>
                    <button class="face-btn" onclick="app.rotateFace('B', false)">B'</button>
                </div>
            </div>

            <div class="control-group">
                <h4>Scramble Options</h4>
                <div class="scramble-controls">
                    <div class="scramble-row">
                        <label for="scramble-length">Length:</label>
                        <input type="number" id="scramble-length" min="5" max="50" value="15" style="width: 60px; padding: 5px; border-radius: 3px; border: 1px solid #ccc;">
                        <button class="action-btn" onclick="app.autoScramble()">🎲 Auto Scramble</button>
                    </div>
                    <div class="scramble-row">
                        <input type="text" id="custom-scramble" placeholder="Enter custom sequence (e.g., R U R' U')"
                               style="flex: 1; padding: 8px; border-radius: 5px; border: 1px solid #ccc; margin-right: 10px;">
                        <button class="action-btn" onclick="app.customScramble()">📝 Apply Custom</button>
                    </div>
                </div>
            </div>

            <div class="control-group">
                <h4>Actions</h4>
                <div class="action-controls">
                    <button class="action-btn" onclick="app.solveCube()">🧠 Solve</button>
                    <button class="action-btn" onclick="app.resetCube()">🔄 Reset</button>
                    <button class="action-btn" onclick="app.undoMove()" id="undo-btn">↩️ Undo</button>
                    <button class="action-btn" onclick="app.debug3DMapping()" style="background: #ff9800;">🔍 Debug 3D</button>
                </div>
            </div>
        </div>

        <div class="status-info">
            <div class="status-item">
                <h4>Cube Status</h4>
                <div id="cube-status">Solved</div>
            </div>
            <div class="status-item">
                <h4>Solver Status</h4>
                <div id="solver-status">Checking...</div>
            </div>
            <div class="status-item">
                <h4>Data Structure</h4>
                <button onclick="app.toggleStateDisplay()" id="state-toggle-btn">Show State</button>
            </div>
        </div>

        <div id="cube-state-display" class="cube-state-section" style="display: none;">
            <h3>🔍 Current Cube State Data</h3>
            <div class="state-tabs">
                <button class="state-tab active" onclick="app.showStateTab('kociemba')">Kociemba String</button>
                <button class="state-tab" onclick="app.showStateTab('faces')">Face Arrays</button>
                <button class="state-tab" onclick="app.showStateTab('positions')">Position Map</button>
            </div>
            <div id="state-content" class="state-content">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>

        <div class="solution-section">
            <h3>💡 Solution</h3>
            <div id="solution-display">
                Ready to solve cubes! Click "Scramble" to mix up the cube, then "Solve" to find the optimal solution.
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="js/move-engine.js"></script>
    <script src="js/cube-state.js"></script>
    <script src="js/cube-3d.js"></script>
    <script src="js/cube-2d.js"></script>
    <script src="js/kociemba-solver.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
