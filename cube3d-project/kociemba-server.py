#!/usr/bin/env python3
"""
Kociemba Solver HTTP Server
Provides HTTP interface to the RubiksCube-TwophaseSolver-master
"""

import sys
import os
import time
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse

# Add the RubiksCube-TwophaseSolver-master directory to Python path
solver_path = os.path.join(os.path.dirname(__file__), '..', 'RubiksCube-TwophaseSolver-master')
if os.path.exists(solver_path):
    sys.path.insert(0, solver_path)
    print(f"✅ Added solver path: {solver_path}")
else:
    print(f"❌ Solver path not found: {solver_path}")

try:
    import solver
    KOCIEMBA_AVAILABLE = True
    print("✅ Kociemba solver imported successfully")
except ImportError as e:
    KOCIEMBA_AVAILABLE = False
    print(f"❌ Failed to import Kociemba solver: {e}")
    print("Please ensure RubiksCube-TwophaseSolver-master is in the parent directory")

class KociembaHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """Handle GET requests for cube solving"""
        try:
            # Parse the URL
            parsed_path = urlparse(self.path)
            path_parts = parsed_path.path.strip('/').split('/')
            
            # Health check endpoint
            if len(path_parts) == 1 and path_parts[0] == 'health':
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                response = {
                    'status': 'healthy',
                    'kociemba_available': KOCIEMBA_AVAILABLE,
                    'timestamp': time.time(),
                    'message': 'Kociemba server is running'
                }
                self.wfile.write(json.dumps(response).encode())
                return
            
            # Cube solving endpoint
            if len(path_parts) == 1 and len(path_parts[0]) == 54:
                cube_string = path_parts[0].upper()
                
                if not KOCIEMBA_AVAILABLE:
                    self.send_error_response("Kociemba solver not available")
                    return
                
                # Validate cube string
                if not self.validate_cube_string(cube_string):
                    self.send_error_response("Invalid cube string format")
                    return
                
                print(f"🧩 Solving cube: {cube_string}")
                
                # Check if already solved
                solved_string = 'UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB'
                if cube_string == solved_string:
                    print("✅ Cube is already solved")
                    self.send_success_response("Already solved!")
                    return
                
                # Solve the cube
                try:
                    start_time = time.time()
                    solution = solver.solve(cube_string, 20, 3)  # max_length=20, timeout=3
                    solve_time = time.time() - start_time
                    
                    print(f"✅ Solution found in {solve_time:.2f}s: {solution}")
                    
                    # Send successful response
                    self.send_success_response(solution)
                    
                except Exception as e:
                    print(f"❌ Solving failed: {e}")
                    self.send_error_response(f"Solving failed: {str(e)}")
                
            else:
                self.send_error_response("Invalid request format. Use: /{54-character-cube-string}")
                
        except Exception as e:
            print(f"❌ Request handling error: {e}")
            self.send_error_response(f"Server error: {str(e)}")
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def validate_cube_string(self, cube_string):
        """Validate that the cube string is in correct format"""
        if len(cube_string) != 54:
            print(f"❌ Invalid length: {len(cube_string)}, expected 54")
            return False
        
        # Check that we have only valid face characters
        valid_chars = set('URFDLB')
        for char in cube_string:
            if char not in valid_chars:
                print(f"❌ Invalid character: {char}")
                return False
        
        # Check that we have exactly 9 of each color
        char_counts = {}
        for char in cube_string:
            char_counts[char] = char_counts.get(char, 0) + 1
        
        for char in valid_chars:
            count = char_counts.get(char, 0)
            if count != 9:
                print(f"❌ Invalid count for {char}: {count}, expected 9")
                return False
        
        return True
    
    def send_success_response(self, solution):
        """Send a successful response"""
        self.send_response(200)
        self.send_header('Content-type', 'text/plain')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(solution.encode())
    
    def send_error_response(self, message):
        """Send an error response"""
        self.send_response(400)
        self.send_header('Content-type', 'text/plain')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(f"Error: {message}".encode())
    
    def log_message(self, format, *args):
        """Override to customize logging"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def run_server(port=8080):
    """Run the Kociemba solver server"""
    print(f"🚀 Starting Kociemba Solver Server on port {port}...")
    
    if not KOCIEMBA_AVAILABLE:
        print("⚠️  Warning: Kociemba solver not available. Server will run but solving will fail.")
        print("   Please ensure RubiksCube-TwophaseSolver-master is in the parent directory.")
    else:
        print("✅ Kociemba solver is ready")
    
    server_address = ('', port)
    httpd = HTTPServer(server_address, KociembaHandler)
    
    print(f"✅ Server running at http://localhost:{port}")
    print("📋 Usage:")
    print(f"   Health check: http://localhost:{port}/health")
    print(f"   Solve cube:   http://localhost:{port}/{{54-character-cube-string}}")
    print("   Example:      http://localhost:{port}/UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB")
    print("\n🔄 Server is ready to accept requests...")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server shutting down...")
        httpd.shutdown()

def check_solver_availability():
    """Check if the solver is properly set up"""
    print("🔍 Checking solver availability...")
    
    if not KOCIEMBA_AVAILABLE:
        print("❌ Solver not available")
        return False
    
    try:
        # Test with a solved cube
        test_cube = 'UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB'
        result = solver.solve(test_cube, 20, 1)
        print(f"✅ Solver test successful: {result}")
        return True
    except Exception as e:
        print(f"❌ Solver test failed: {e}")
        return False

if __name__ == '__main__':
    port = 8081  # Changed from 8080 to avoid permission issues

    # Parse command line arguments
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ Invalid port number. Using default port 8081.")
    
    # Check if solver directory exists
    if not os.path.exists(solver_path):
        print(f"❌ Solver directory not found: {solver_path}")
        print("Please ensure RubiksCube-TwophaseSolver-master is in the parent directory.")
        print("Expected structure:")
        print("  cube3d-project/")
        print("    kociemba-server.py")
        print("  RubiksCube-TwophaseSolver-master/")
        print("    solver.py")
        print("    ...")
        sys.exit(1)
    
    # Check solver availability
    if KOCIEMBA_AVAILABLE:
        print("🧪 Testing solver...")
        if not check_solver_availability():
            print("⚠️  Solver test failed, but starting server anyway...")
    
    run_server(port)
