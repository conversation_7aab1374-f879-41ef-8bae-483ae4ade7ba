/**
 * 3x3 Rubik's Cube State Management
 * Based on the Kociemba representation from RubiksCube-TwophaseSolver-master
 */

class CubeState {
    constructor() {
        // Initialize solved cube in Kociemba format
        // Order: U(0-8), R(9-17), F(18-26), D(27-35), L(36-44), B(45-53)
        this.reset();
        this.moveHistory = [];
    }

    reset() {
        /**
         * Reset to solved state
         * U=White, R=Red, F=Green, D=Yellow, L=Orange, B=Blue
         */
        this.facelets = [];
        
        // Up face (White) - positions 0-8
        for (let i = 0; i < 9; i++) this.facelets.push('U');
        // Right face (Red) - positions 9-17
        for (let i = 0; i < 9; i++) this.facelets.push('R');
        // Front face (Green) - positions 18-26
        for (let i = 0; i < 9; i++) this.facelets.push('F');
        // Down face (Yellow) - positions 27-35
        for (let i = 0; i < 9; i++) this.facelets.push('D');
        // Left face (Orange) - positions 36-44
        for (let i = 0; i < 9; i++) this.facelets.push('L');
        // Back face (Blue) - positions 45-53
        for (let i = 0; i < 9; i++) this.facelets.push('B');
        
        this.moveHistory = [];
    }

    toString() {
        /**
         * Get Kociemba format string
         */
        return this.facelets.join('');
    }

    isSolved() {
        /**
         * Check if cube is in solved state
         */
        const solved = 'UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB';
        return this.toString() === solved;
    }

    getFaces() {
        /**
         * Get faces as 2D arrays for visualization
         */
        const faces = {};
        const faceNames = ['U', 'R', 'F', 'D', 'L', 'B'];
        
        for (let f = 0; f < 6; f++) {
            const faceName = faceNames[f];
            faces[faceName] = [];
            
            for (let row = 0; row < 3; row++) {
                faces[faceName][row] = [];
                for (let col = 0; col < 3; col++) {
                    const index = f * 9 + row * 3 + col;
                    faces[faceName][row][col] = this.facelets[index];
                }
            }
        }
        
        return faces;
    }

    getColorForFacelet(facelet) {
        /**
         * Convert facelet character to color name
         */
        const colorMap = {
            'U': 'white',
            'R': 'red', 
            'F': 'green',
            'D': 'yellow',
            'L': 'orange',
            'B': 'blue'
        };
        return colorMap[facelet] || 'gray';
    }

    executeMove(face, clockwise = true) {
        /**
         * Execute a face rotation
         */
        console.log(`Executing move: ${face}${clockwise ? '' : "'"}`);
        
        // Store move in history
        this.moveHistory.push(`${face}${clockwise ? '' : "'"}`);
        
        // Rotate the face itself
        this.rotateFace(face, clockwise);
        
        // Rotate adjacent edges
        this.rotateEdges(face, clockwise);
        
        return true;
    }

    rotateFace(face, clockwise) {
        /**
         * Rotate a face 90 degrees
         */
        const faceIndex = this.getFaceIndex(face);
        const start = faceIndex * 9;
        
        // Get current face state
        const current = [];
        for (let i = 0; i < 9; i++) {
            current[i] = this.facelets[start + i];
        }
        
        // Apply rotation
        if (clockwise) {
            // Clockwise: 0->2, 1->5, 2->8, 3->1, 4->4, 5->7, 6->0, 7->3, 8->6
            this.facelets[start + 0] = current[6];
            this.facelets[start + 1] = current[3];
            this.facelets[start + 2] = current[0];
            this.facelets[start + 3] = current[7];
            this.facelets[start + 4] = current[4];
            this.facelets[start + 5] = current[1];
            this.facelets[start + 6] = current[8];
            this.facelets[start + 7] = current[5];
            this.facelets[start + 8] = current[2];
        } else {
            // Counter-clockwise: reverse of clockwise
            this.facelets[start + 0] = current[2];
            this.facelets[start + 1] = current[5];
            this.facelets[start + 2] = current[8];
            this.facelets[start + 3] = current[1];
            this.facelets[start + 4] = current[4];
            this.facelets[start + 5] = current[7];
            this.facelets[start + 6] = current[0];
            this.facelets[start + 7] = current[3];
            this.facelets[start + 8] = current[6];
        }
    }

    rotateEdges(face, clockwise) {
        /**
         * Rotate the edges adjacent to a face
         * Using correct Kociemba position mappings
         */
        const edgeCycles = {
            'U': [
                [18, 19, 20], // F top row
                [9, 10, 11],  // R top row
                [45, 46, 47], // B top row
                [36, 37, 38]  // L top row
            ],
            'D': [
                [24, 25, 26], // F bottom row
                [42, 43, 44], // L bottom row
                [51, 52, 53], // B bottom row
                [15, 16, 17]  // R bottom row
            ],
            'F': [
                [6, 7, 8],    // U bottom row
                [9, 12, 15],  // R left column
                [29, 28, 27], // D top row (reversed)
                [44, 41, 38]  // L right column (reversed)
            ],
            'B': [
                [0, 1, 2],    // U top row
                [17, 14, 11], // R right column (reversed)
                [35, 34, 33], // D bottom row (reversed)
                [36, 39, 42]  // L left column
            ],
            'R': [
                [2, 5, 8],    // U right column
                [20, 23, 26], // F right column
                [29, 32, 35], // D right column
                [53, 50, 47]  // B left column (reversed)
            ],
            'L': [
                [0, 3, 6],    // U left column
                [45, 48, 51], // B right column (reversed)
                [27, 30, 33], // D left column
                [18, 21, 24]  // F left column
            ]
        };

        const cycle = edgeCycles[face];
        if (!cycle) return;

        // Save first position
        const temp = [
            this.facelets[cycle[0][0]],
            this.facelets[cycle[0][1]],
            this.facelets[cycle[0][2]]
        ];

        if (clockwise) {
            // Rotate clockwise: 0->1->2->3->0
            for (let i = 0; i < 3; i++) {
                this.facelets[cycle[0][i]] = this.facelets[cycle[3][i]];
                this.facelets[cycle[3][i]] = this.facelets[cycle[2][i]];
                this.facelets[cycle[2][i]] = this.facelets[cycle[1][i]];
                this.facelets[cycle[1][i]] = temp[i];
            }
        } else {
            // Rotate counter-clockwise: 0->3->2->1->0
            for (let i = 0; i < 3; i++) {
                this.facelets[cycle[0][i]] = this.facelets[cycle[1][i]];
                this.facelets[cycle[1][i]] = this.facelets[cycle[2][i]];
                this.facelets[cycle[2][i]] = this.facelets[cycle[3][i]];
                this.facelets[cycle[3][i]] = temp[i];
            }
        }
    }

    getFaceIndex(face) {
        /**
         * Get the starting index for a face
         */
        const faceIndices = { 'U': 0, 'R': 1, 'F': 2, 'D': 3, 'L': 4, 'B': 5 };
        return faceIndices[face];
    }

    scramble(moves = 20) {
        /**
         * Generate a random scramble
         */
        const faces = ['U', 'R', 'F', 'D', 'L', 'B'];
        const scrambleSequence = [];
        
        for (let i = 0; i < moves; i++) {
            const face = faces[Math.floor(Math.random() * faces.length)];
            const clockwise = Math.random() > 0.5;
            
            this.executeMove(face, clockwise);
            scrambleSequence.push(`${face}${clockwise ? '' : "'"}`);
        }
        
        return scrambleSequence.join(' ');
    }

    applyMoveSequence(sequence) {
        /**
         * Apply a sequence of moves
         */
        const moves = sequence.trim().split(/\s+/);
        
        for (const move of moves) {
            if (move.length === 0) continue;
            
            const face = move[0];
            const clockwise = !move.includes("'");
            
            this.executeMove(face, clockwise);
        }
    }

    validate() {
        /**
         * Validate cube state
         */
        if (this.facelets.length !== 54) {
            return false;
        }
        
        // Count each color
        const counts = { 'U': 0, 'R': 0, 'F': 0, 'D': 0, 'L': 0, 'B': 0 };
        
        for (const facelet of this.facelets) {
            if (counts[facelet] !== undefined) {
                counts[facelet]++;
            } else {
                return false;
            }
        }
        
        // Each color should appear exactly 9 times
        for (const count of Object.values(counts)) {
            if (count !== 9) {
                return false;
            }
        }
        
        return true;
    }

    getMoveHistory() {
        /**
         * Get the history of moves
         */
        return [...this.moveHistory];
    }

    clearMoveHistory() {
        /**
         * Clear move history
         */
        this.moveHistory = [];
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.CubeState = CubeState;
} else {
    module.exports = CubeState;
}
