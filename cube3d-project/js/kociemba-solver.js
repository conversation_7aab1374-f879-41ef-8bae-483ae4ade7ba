/**
 * Kociemba Solver Interface
 * Communicates with the Python Kociemba server for optimal cube solving
 */

class KociembaSolver {
    constructor() {
        this.serverUrl = 'http://localhost:8080';
        this.isServerRunning = false;
        this.maxLength = 20;
        this.timeout = 3;
    }

    async checkServer() {
        /**
         * Check if the Kociemba server is running
         */
        try {
            const response = await fetch(`${this.serverUrl}/health`, {
                method: 'GET',
                timeout: 2000
            });
            
            if (response.ok) {
                this.isServerRunning = true;
                console.log('✅ Kociemba server is running');
                return true;
            }
        } catch (error) {
            this.isServerRunning = false;
            console.log('❌ Kociemba server not available');
        }
        return false;
    }

    async solve(cubeString, maxLength = 20, timeout = 3) {
        /**
         * Solve a cube using the Kociemba two-phase algorithm
         */
        
        // Check if already solved
        const solvedString = 'UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB';
        if (cubeString === solvedString) {
            return 'Already solved!';
        }

        // Try server-based solving
        if (await this.checkServer()) {
            return await this.solveWithServer(cubeString, maxLength, timeout);
        }
        
        // Fallback message
        return 'Kociemba server not available. Please start the server: python start_server.py 8080 20 3';
    }

    async solveWithServer(cubeString, maxLength = 20, timeout = 3) {
        /**
         * Solve using the Python Kociemba server
         */
        try {
            const url = `${this.serverUrl}/${cubeString}`;
            console.log(`🧩 Sending request to: ${url}`);
            
            const response = await fetch(url, {
                method: 'GET',
                timeout: timeout * 1000
            });

            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }

            const solution = await response.text();
            console.log('📥 Server solution:', solution);
            
            // Parse and clean the solution
            return this.parseSolution(solution);
            
        } catch (error) {
            console.error('❌ Server solving failed:', error);
            throw new Error(`Server solving failed: ${error.message}`);
        }
    }

    parseSolution(solutionString) {
        /**
         * Parse the solution string from the server
         * Convert from Kociemba format to standard format
         */
        if (solutionString.includes('Error') || solutionString.includes('error')) {
            return solutionString;
        }

        // Remove the move count at the end (e.g., "(19f)")
        let solution = solutionString.replace(/\s*\(\d+f?\)\s*$/, '').trim();
        
        // The Kociemba solver should already return standard notation
        // But we can clean it up if needed
        solution = solution.replace(/\s+/g, ' ').trim();
        
        return solution;
    }

    parseMoves(solutionString) {
        /**
         * Parse solution string into individual moves
         */
        if (!solutionString || solutionString.includes('Error') || solutionString.includes('server')) {
            return [];
        }

        return solutionString.trim().split(/\s+/).filter(move => move.length > 0);
    }

    async solveCube(cubeState) {
        /**
         * Solve a CubeState object
         */
        try {
            const cubeString = cubeState.toString();
            console.log('🔄 Converting cube state to Kociemba format:', cubeString);

            // Validate cube string
            if (!this.validateCubeString(cubeString)) {
                throw new Error('Invalid cube state - cannot solve');
            }

            // Solve using Kociemba algorithm
            const solution = await this.solve(cubeString, this.maxLength, this.timeout);
            
            return {
                success: true,
                solution: solution,
                moves: this.parseMoves(solution),
                algorithm: 'Kociemba Two-Phase',
                cubeString: cubeString
            };

        } catch (error) {
            console.error('❌ Solving failed:', error);
            return {
                success: false,
                error: error.message,
                solution: null
            };
        }
    }

    validateCubeString(cubeString) {
        /**
         * Validate that the cube string is in correct Kociemba format
         */
        if (cubeString.length !== 54) {
            return false;
        }
        
        // Check that we have only valid face characters
        const validChars = new Set(['U', 'R', 'F', 'D', 'L', 'B']);
        for (const char of cubeString) {
            if (!validChars.has(char)) {
                return false;
            }
        }
        
        // Check that we have exactly 9 of each color
        const charCounts = {};
        for (const char of cubeString) {
            charCounts[char] = (charCounts[char] || 0) + 1;
        }
        
        for (const char of validChars) {
            if (charCounts[char] !== 9) {
                return false;
            }
        }
        
        return true;
    }

    getServerStatus() {
        /**
         * Get current server status
         */
        return {
            isRunning: this.isServerRunning,
            url: this.serverUrl,
            maxLength: this.maxLength,
            timeout: this.timeout
        };
    }

    getSetupInstructions() {
        /**
         * Get instructions for setting up the Kociemba server
         */
        return `
🔧 Kociemba Server Setup Instructions:

1. Ensure you have the RubiksCube-TwophaseSolver-master folder
2. Navigate to the folder in terminal/command prompt
3. Run: python start_server.py 8080 20 3
4. Wait for "Server now listening..." message
5. First run may take 30+ minutes to generate lookup tables

Alternative quick setup:
- Use the provided server script: python kociemba-server.py 8080

The server will be available at: ${this.serverUrl}
        `.trim();
    }

    setServerConfig(url, maxLength = 20, timeout = 3) {
        /**
         * Configure server settings
         */
        this.serverUrl = url;
        this.maxLength = maxLength;
        this.timeout = timeout;
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.KociembaSolver = KociembaSolver;
} else {
    module.exports = KociembaSolver;
}
