/**
 * 3D Cube Visualization using Three.js
 * Renders a 3x3 Rubik's cube with proper face rotations
 */

class Cube3D {
    constructor(container, cubeState) {
        this.container = container;
        this.cubeState = cubeState;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.cubeGroup = null;
        this.cubies = [];
        this.isAnimating = false;
        
        this.init();
    }

    init() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x222222);

        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75, 
            this.container.clientWidth / this.container.clientHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(4, 4, 4);
        this.camera.lookAt(0, 0, 0);

        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.container.appendChild(this.renderer.domElement);

        // Add lights
        this.addLights();

        // Create cube group
        this.cubeGroup = new THREE.Group();
        this.scene.add(this.cubeGroup);

        // Create cubies
        this.createCubies();

        // Add controls
        this.addControls();

        // Start render loop
        this.animate();

        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
    }

    addLights() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // Additional lights for better visibility
        const light2 = new THREE.DirectionalLight(0xffffff, 0.4);
        light2.position.set(-10, -10, -5);
        this.scene.add(light2);
    }

    createCubies() {
        const cubeSize = 0.95;
        const gap = 0.05;
        const positions = [];

        // Generate positions for 3x3x3 cube
        for (let x = -1; x <= 1; x++) {
            for (let y = -1; y <= 1; y++) {
                for (let z = -1; z <= 1; z++) {
                    positions.push({
                        x: x * (cubeSize + gap),
                        y: y * (cubeSize + gap),
                        z: z * (cubeSize + gap),
                        gridX: x + 1,
                        gridY: y + 1,
                        gridZ: z + 1
                    });
                }
            }
        }

        // Create geometry and materials
        const geometry = new THREE.BoxGeometry(cubeSize, cubeSize, cubeSize);
        
        // Create cubies
        positions.forEach(pos => {
            const materials = this.createCubieMaterials(pos.gridX, pos.gridY, pos.gridZ);
            const cubie = new THREE.Mesh(geometry, materials);
            
            cubie.position.set(pos.x, pos.y, pos.z);
            cubie.castShadow = true;
            cubie.receiveShadow = true;
            
            // Store grid position for face identification
            cubie.userData = {
                x: pos.gridX,
                y: pos.gridY,
                z: pos.gridZ
            };
            
            this.cubies.push(cubie);
            this.cubeGroup.add(cubie);
        });

        this.updateColors();
    }

    createCubieMaterials(x, y, z) {
        // Create materials for each face of the cubie
        // Order: right(+X), left(-X), top(+Y), bottom(-Y), front(+Z), back(-Z)
        const materials = [];

        // Get the cube state as a flat array (Kociemba format)
        const facelets = this.cubeState.facelets;

        // Right face (+X, x = 2)
        if (x === 2) {
            // R face: positions 9-17, arranged as 3x3 grid
            // Looking at R face from outside the cube
            const row = 2 - y; // y=2->row=0, y=1->row=1, y=0->row=2
            const col = 2 - z; // z=2->col=0, z=1->col=1, z=0->col=2 (view from outside)
            const index = 9 + row * 3 + col;
            const facelet = facelets[index];
            const color = this.cubeState.getColorForFacelet(facelet);
            materials.push(this.createColorMaterial(color));
        } else {
            materials.push(this.createColorMaterial('black'));
        }

        // Left face (-X, x = 0)
        if (x === 0) {
            // L face: positions 36-44, arranged as 3x3 grid
            // Looking at L face from outside the cube
            const row = 2 - y; // y=2->row=0, y=1->row=1, y=0->row=2
            const col = z;     // z=0->col=0, z=1->col=1, z=2->col=2 (view from outside)
            const index = 36 + row * 3 + col;
            const facelet = facelets[index];
            const color = this.cubeState.getColorForFacelet(facelet);
            materials.push(this.createColorMaterial(color));
        } else {
            materials.push(this.createColorMaterial('black'));
        }

        // Top face (+Y, y = 2)
        if (y === 2) {
            // U face: positions 0-8, arranged as 3x3 grid
            const row = 2 - z; // z=2->row=0, z=1->row=1, z=0->row=2
            const col = x;     // x=0->col=0, x=1->col=1, x=2->col=2
            const index = 0 + row * 3 + col;
            const facelet = facelets[index];
            const color = this.cubeState.getColorForFacelet(facelet);
            materials.push(this.createColorMaterial(color));
        } else {
            materials.push(this.createColorMaterial('black'));
        }

        // Bottom face (-Y, y = 0)
        if (y === 0) {
            // D face: positions 27-35, arranged as 3x3 grid
            const row = z;     // z=0->row=0, z=1->row=1, z=2->row=2
            const col = x;     // x=0->col=0, x=1->col=1, x=2->col=2
            const index = 27 + row * 3 + col;
            const facelet = facelets[index];
            const color = this.cubeState.getColorForFacelet(facelet);
            materials.push(this.createColorMaterial(color));
        } else {
            materials.push(this.createColorMaterial('black'));
        }

        // Front face (+Z, z = 2)
        if (z === 2) {
            // F face: positions 18-26, arranged as 3x3 grid
            const row = 2 - y; // y=2->row=0, y=1->row=1, y=0->row=2
            const col = x;     // x=0->col=0, x=1->col=1, x=2->col=2
            const index = 18 + row * 3 + col;
            const facelet = facelets[index];
            const color = this.cubeState.getColorForFacelet(facelet);
            materials.push(this.createColorMaterial(color));
        } else {
            materials.push(this.createColorMaterial('black'));
        }

        // Back face (-Z, z = 0)
        if (z === 0) {
            // B face: positions 45-53, arranged as 3x3 grid
            const row = 2 - y; // y=2->row=0, y=1->row=1, y=0->row=2
            const col = 2 - x; // x=2->col=0, x=1->col=1, x=0->col=2 (mirrored)
            const index = 45 + row * 3 + col;
            const facelet = facelets[index];
            const color = this.cubeState.getColorForFacelet(facelet);
            materials.push(this.createColorMaterial(color));
        } else {
            materials.push(this.createColorMaterial('black'));
        }

        return materials;
    }



    createColorMaterial(colorName) {
        const colors = {
            'white': 0xffffff,
            'yellow': 0xffff00,
            'red': 0xff0000,
            'orange': 0xff8000,
            'green': 0x00ff00,
            'blue': 0x0000ff,
            'black': 0x000000,
            'gray': 0x808080
        };
        
        const color = colors[colorName] || colors['gray'];
        
        return new THREE.MeshLambertMaterial({
            color: color,
            transparent: colorName === 'black',
            opacity: colorName === 'black' ? 0.1 : 1.0
        });
    }

    updateColors() {
        this.cubies.forEach(cubie => {
            const { x, y, z } = cubie.userData;
            const newMaterials = this.createCubieMaterials(x, y, z);
            
            // Dispose old materials
            if (Array.isArray(cubie.material)) {
                cubie.material.forEach(mat => mat.dispose());
            } else {
                cubie.material.dispose();
            }
            
            cubie.material = newMaterials;
        });
    }

    async animateFaceRotation(face, clockwise, duration = 300) {
        if (this.isAnimating) return;

        this.isAnimating = true;

        return new Promise((resolve) => {
            // Instead of moving cubies, just update colors after a delay
            // This prevents the 3D deformation issue
            setTimeout(() => {
                this.updateColors();
                this.isAnimating = false;
                resolve();
            }, duration);
        });
    }

    getRotatingCubies(face) {
        return this.cubies.filter(cubie => {
            const { x, y, z } = cubie.userData;
            switch (face) {
                case 'U': return y === 2;
                case 'D': return y === 0;
                case 'F': return z === 2;
                case 'B': return z === 0;
                case 'R': return x === 2;
                case 'L': return x === 0;
                default: return false;
            }
        });
    }

    getRotationAxis(face) {
        switch (face) {
            case 'U':
            case 'D':
                return 'y';
            case 'F':
            case 'B':
                return 'z';
            case 'R':
            case 'L':
                return 'x';
            default:
                return 'y';
        }
    }

    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    addControls() {
        let isDragging = false;
        let previousMousePosition = { x: 0, y: 0 };

        this.renderer.domElement.addEventListener('mousedown', (e) => {
            isDragging = true;
            previousMousePosition = { x: e.clientX, y: e.clientY };
        });

        this.renderer.domElement.addEventListener('mousemove', (e) => {
            if (!isDragging || this.isAnimating) return;

            const deltaMove = {
                x: e.clientX - previousMousePosition.x,
                y: e.clientY - previousMousePosition.y
            };

            this.cubeGroup.rotation.y += deltaMove.x * 0.01;
            this.cubeGroup.rotation.x += deltaMove.y * 0.01;

            previousMousePosition = { x: e.clientX, y: e.clientY };
        });

        this.renderer.domElement.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Zoom with mouse wheel
        this.renderer.domElement.addEventListener('wheel', (e) => {
            e.preventDefault();
            const zoom = e.deltaY > 0 ? 1.1 : 0.9;
            this.camera.position.multiplyScalar(zoom);
        });
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        this.renderer.render(this.scene, this.camera);
    }

    onWindowResize() {
        this.camera.aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    }

    refresh() {
        this.updateColors();
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.Cube3D = Cube3D;
} else {
    module.exports = Cube3D;
}
