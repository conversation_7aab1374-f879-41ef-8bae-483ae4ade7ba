/**
 * 3D Cube Visualization using Three.js
 * Renders a 3x3 Rubik's cube with proper face rotations
 */

class Cube3D {
    constructor(container, cubeState) {
        this.container = container;
        this.cubeState = cubeState;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.cubeGroup = null;
        this.cubies = [];
        this.isAnimating = false;
        
        this.init();
    }

    init() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x222222);

        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75, 
            this.container.clientWidth / this.container.clientHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(4, 4, 4);
        this.camera.lookAt(0, 0, 0);

        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.container.appendChild(this.renderer.domElement);

        // Add lights
        this.addLights();

        // Create cube group
        this.cubeGroup = new THREE.Group();
        this.scene.add(this.cubeGroup);

        // Create cubies
        this.createCubies();

        // Add controls
        this.addControls();

        // Start render loop
        this.animate();

        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
    }

    addLights() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // Additional lights for better visibility
        const light2 = new THREE.DirectionalLight(0xffffff, 0.4);
        light2.position.set(-10, -10, -5);
        this.scene.add(light2);
    }

    createCubies() {
        const cubeSize = 0.95;
        const gap = 0.05;
        const positions = [];

        // Generate positions for 3x3x3 cube
        for (let x = -1; x <= 1; x++) {
            for (let y = -1; y <= 1; y++) {
                for (let z = -1; z <= 1; z++) {
                    positions.push({
                        x: x * (cubeSize + gap),
                        y: y * (cubeSize + gap),
                        z: z * (cubeSize + gap),
                        gridX: x + 1,
                        gridY: y + 1,
                        gridZ: z + 1
                    });
                }
            }
        }

        // Create geometry and materials
        const geometry = new THREE.BoxGeometry(cubeSize, cubeSize, cubeSize);
        
        // Create cubies
        positions.forEach(pos => {
            const materials = this.createCubieMaterials(pos.gridX, pos.gridY, pos.gridZ);
            const cubie = new THREE.Mesh(geometry, materials);
            
            cubie.position.set(pos.x, pos.y, pos.z);
            cubie.castShadow = true;
            cubie.receiveShadow = true;
            
            // Store grid position for face identification
            cubie.userData = {
                x: pos.gridX,
                y: pos.gridY,
                z: pos.gridZ
            };
            
            this.cubies.push(cubie);
            this.cubeGroup.add(cubie);
        });

        this.updateColors();
    }

    createCubieMaterials(x, y, z) {
        // Create materials for each face of the cubie
        // Order: right, left, top, bottom, front, back
        const materials = [];
        
        const faces = this.cubeState.getFaces();
        
        // Right face (x = 2)
        if (x === 2) {
            const color = this.getFaceColor('R', 2-z, y);
            materials.push(this.createColorMaterial(color));
        } else {
            materials.push(this.createColorMaterial('black'));
        }
        
        // Left face (x = 0)
        if (x === 0) {
            const color = this.getFaceColor('L', z, y);
            materials.push(this.createColorMaterial(color));
        } else {
            materials.push(this.createColorMaterial('black'));
        }
        
        // Top face (y = 2)
        if (y === 2) {
            const color = this.getFaceColor('U', 2-z, x);
            materials.push(this.createColorMaterial(color));
        } else {
            materials.push(this.createColorMaterial('black'));
        }
        
        // Bottom face (y = 0)
        if (y === 0) {
            const color = this.getFaceColor('D', z, x);
            materials.push(this.createColorMaterial(color));
        } else {
            materials.push(this.createColorMaterial('black'));
        }
        
        // Front face (z = 2)
        if (z === 2) {
            const color = this.getFaceColor('F', 2-y, x);
            materials.push(this.createColorMaterial(color));
        } else {
            materials.push(this.createColorMaterial('black'));
        }
        
        // Back face (z = 0)
        if (z === 0) {
            const color = this.getFaceColor('B', 2-y, 2-x);
            materials.push(this.createColorMaterial(color));
        } else {
            materials.push(this.createColorMaterial('black'));
        }
        
        return materials;
    }

    getFaceColor(face, row, col) {
        const faces = this.cubeState.getFaces();
        if (faces[face] && faces[face][row] && faces[face][row][col]) {
            return this.cubeState.getColorForFacelet(faces[face][row][col]);
        }
        return 'gray';
    }

    createColorMaterial(colorName) {
        const colors = {
            'white': 0xffffff,
            'yellow': 0xffff00,
            'red': 0xff0000,
            'orange': 0xff8000,
            'green': 0x00ff00,
            'blue': 0x0000ff,
            'black': 0x000000,
            'gray': 0x808080
        };
        
        const color = colors[colorName] || colors['gray'];
        
        return new THREE.MeshLambertMaterial({
            color: color,
            transparent: colorName === 'black',
            opacity: colorName === 'black' ? 0.1 : 1.0
        });
    }

    updateColors() {
        this.cubies.forEach(cubie => {
            const { x, y, z } = cubie.userData;
            const newMaterials = this.createCubieMaterials(x, y, z);
            
            // Dispose old materials
            if (Array.isArray(cubie.material)) {
                cubie.material.forEach(mat => mat.dispose());
            } else {
                cubie.material.dispose();
            }
            
            cubie.material = newMaterials;
        });
    }

    async animateFaceRotation(face, clockwise, duration = 300) {
        if (this.isAnimating) return;
        
        this.isAnimating = true;
        
        return new Promise((resolve) => {
            const rotatingCubies = this.getRotatingCubies(face);
            const rotationGroup = new THREE.Group();
            this.scene.add(rotationGroup);
            
            // Move cubies to rotation group
            rotatingCubies.forEach(cubie => {
                const worldPosition = new THREE.Vector3();
                cubie.getWorldPosition(worldPosition);
                this.cubeGroup.remove(cubie);
                rotationGroup.add(cubie);
                cubie.position.copy(worldPosition);
            });
            
            // Animate rotation
            const startTime = Date.now();
            const axis = this.getRotationAxis(face);
            const angle = clockwise ? Math.PI / 2 : -Math.PI / 2;
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const easeProgress = this.easeInOutCubic(progress);
                
                rotationGroup.rotation[axis] = angle * easeProgress;
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    // Animation complete
                    rotatingCubies.forEach(cubie => {
                        const worldPosition = new THREE.Vector3();
                        cubie.getWorldPosition(worldPosition);
                        rotationGroup.remove(cubie);
                        this.cubeGroup.add(cubie);
                        cubie.position.copy(worldPosition);
                    });
                    
                    this.scene.remove(rotationGroup);
                    this.isAnimating = false;
                    resolve();
                }
            };
            
            animate();
        });
    }

    getRotatingCubies(face) {
        return this.cubies.filter(cubie => {
            const { x, y, z } = cubie.userData;
            switch (face) {
                case 'U': return y === 2;
                case 'D': return y === 0;
                case 'F': return z === 2;
                case 'B': return z === 0;
                case 'R': return x === 2;
                case 'L': return x === 0;
                default: return false;
            }
        });
    }

    getRotationAxis(face) {
        switch (face) {
            case 'U':
            case 'D':
                return 'y';
            case 'F':
            case 'B':
                return 'z';
            case 'R':
            case 'L':
                return 'x';
            default:
                return 'y';
        }
    }

    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    addControls() {
        let isDragging = false;
        let previousMousePosition = { x: 0, y: 0 };

        this.renderer.domElement.addEventListener('mousedown', (e) => {
            isDragging = true;
            previousMousePosition = { x: e.clientX, y: e.clientY };
        });

        this.renderer.domElement.addEventListener('mousemove', (e) => {
            if (!isDragging || this.isAnimating) return;

            const deltaMove = {
                x: e.clientX - previousMousePosition.x,
                y: e.clientY - previousMousePosition.y
            };

            this.cubeGroup.rotation.y += deltaMove.x * 0.01;
            this.cubeGroup.rotation.x += deltaMove.y * 0.01;

            previousMousePosition = { x: e.clientX, y: e.clientY };
        });

        this.renderer.domElement.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Zoom with mouse wheel
        this.renderer.domElement.addEventListener('wheel', (e) => {
            e.preventDefault();
            const zoom = e.deltaY > 0 ? 1.1 : 0.9;
            this.camera.position.multiplyScalar(zoom);
        });
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        this.renderer.render(this.scene, this.camera);
    }

    onWindowResize() {
        this.camera.aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    }

    refresh() {
        this.updateColors();
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.Cube3D = Cube3D;
} else {
    module.exports = Cube3D;
}
