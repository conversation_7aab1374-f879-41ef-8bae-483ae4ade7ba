/**
 * 2D Cube Visualization
 * Displays the cube as a flat net layout
 */

class Cube2D {
    constructor(container, cubeState) {
        this.container = container;
        this.cubeState = cubeState;
        this.init();
    }

    init() {
        this.render();
    }

    render() {
        this.container.innerHTML = '';
        
        const faces = this.cubeState.getFaces();
        const faceOrder = ['U', 'L', 'F', 'R', 'D', 'B'];
        
        faceOrder.forEach(faceName => {
            const faceElement = this.createFaceElement(faces[faceName], faceName);
            this.container.appendChild(faceElement);
        });
    }

    createFaceElement(face, faceName) {
        const faceDiv = document.createElement('div');
        faceDiv.className = `face face-${faceName}`;

        // Create face label
        const label = document.createElement('div');
        label.className = 'face-label';
        label.textContent = faceName;
        label.style.cssText = `
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            color: #ffeb3b;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            z-index: 10;
        `;
        faceDiv.appendChild(label);

        // Create stickers
        for (let row = 0; row < 3; row++) {
            for (let col = 0; col < 3; col++) {
                const sticker = document.createElement('div');
                sticker.className = 'sticker';

                const facelet = face[row][col];
                const colorName = this.cubeState.getColorForFacelet(facelet);
                sticker.classList.add(`color-${colorName}`);

                // Add position info for debugging
                sticker.title = `${faceName}[${row}][${col}] = ${facelet}`;

                // Add hover effects
                sticker.addEventListener('mouseenter', () => {
                    sticker.style.transform = 'scale(1.1)';
                    sticker.style.zIndex = '10';
                    sticker.style.boxShadow = '0 0 10px rgba(255, 235, 59, 0.8)';
                });

                sticker.addEventListener('mouseleave', () => {
                    sticker.style.transform = 'scale(1)';
                    sticker.style.zIndex = '1';
                    sticker.style.boxShadow = 'none';
                });

                faceDiv.appendChild(sticker);
            }
        }

        return faceDiv;
    }

    refresh() {
        this.render();
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.Cube2D = Cube2D;
} else {
    module.exports = Cube2D;
}
