/**
 * Advanced Move Engine for 3x3 Rubik's Cube
 * Handles rotations, permutation tracking, and move validation
 */

class MoveEngine {
    constructor(cubeState) {
        this.cubeState = cubeState;
        this.moveHistory = [];
        this.permutationHistory = [];
        this.undoStack = [];
        
        // Define move mappings for each face
        this.moveMappings = this.initializeMoveMappings();
    }

    initializeMoveMappings() {
        /**
         * Define how each face rotation affects the cube
         * Each move maps old positions to new positions
         */
        return {
            'U': {
                face: [6, 3, 0, 7, 4, 1, 8, 5, 2], // Face rotation (clockwise)
                edges: {
                    // F-top -> R-top -> B-top -> L-top -> F-top
                    cycle: [[18, 19, 20], [9, 10, 11], [45, 46, 47], [36, 37, 38]]
                }
            },
            'D': {
                face: [6, 3, 0, 7, 4, 1, 8, 5, 2], // Face rotation (clockwise)
                edges: {
                    // F-bottom -> L-bottom -> B-bottom -> R-bottom -> F-bottom
                    cycle: [[24, 25, 26], [42, 43, 44], [51, 52, 53], [15, 16, 17]]
                }
            },
            'F': {
                face: [6, 3, 0, 7, 4, 1, 8, 5, 2], // Face rotation (clockwise)
                edges: {
                    // U-bottom -> R-left -> D-top -> L-right -> U-bottom
                    cycle: [[6, 7, 8], [9, 12, 15], [29, 28, 27], [44, 41, 38]]
                }
            },
            'B': {
                face: [6, 3, 0, 7, 4, 1, 8, 5, 2], // Face rotation (clockwise)
                edges: {
                    // U-top -> L-left -> D-bottom -> R-right -> U-top
                    cycle: [[0, 1, 2], [36, 39, 42], [35, 34, 33], [17, 14, 11]]
                }
            },
            'R': {
                face: [6, 3, 0, 7, 4, 1, 8, 5, 2], // Face rotation (clockwise)
                edges: {
                    // U-right -> F-right -> D-right -> B-left -> U-right
                    cycle: [[2, 5, 8], [20, 23, 26], [29, 32, 35], [53, 50, 47]]
                }
            },
            'L': {
                face: [6, 3, 0, 7, 4, 1, 8, 5, 2], // Face rotation (clockwise)
                edges: {
                    // U-left -> B-right -> D-left -> F-left -> U-left
                    cycle: [[0, 3, 6], [45, 48, 51], [27, 30, 33], [18, 21, 24]]
                }
            }
        };
    }

    executeMove(face, clockwise = true, recordHistory = true) {
        /**
         * Execute a move with full permutation tracking
         */
        console.log(`🔄 Move Engine: Executing ${face}${clockwise ? '' : "'"}`);
        
        // Save current state for undo
        if (recordHistory) {
            this.undoStack.push({
                state: this.cubeState.facelets.slice(),
                move: `${face}${clockwise ? '' : "'"}`,
                timestamp: Date.now()
            });
        }

        // Apply the move
        const permutation = this.applyMove(face, clockwise);
        
        // Record move and permutation
        if (recordHistory) {
            this.moveHistory.push(`${face}${clockwise ? '' : "'"}`);
            this.permutationHistory.push(permutation);
        }

        return permutation;
    }

    applyMove(face, clockwise) {
        /**
         * Apply a single move and return the permutation applied
         */
        const mapping = this.moveMappings[face];
        if (!mapping) {
            throw new Error(`Invalid face: ${face}`);
        }

        const permutation = [];
        const facelets = this.cubeState.facelets;
        const oldState = facelets.slice();

        // Rotate the face itself
        const faceStart = this.getFaceStartIndex(face);
        const faceMapping = clockwise ? mapping.face : this.invertMapping(mapping.face);
        
        for (let i = 0; i < 9; i++) {
            const newPos = faceStart + i;
            const oldPos = faceStart + faceMapping[i];
            facelets[newPos] = oldState[oldPos];
            permutation.push({ from: oldPos, to: newPos });
        }

        // Rotate the edges
        const edgeCycle = mapping.edges.cycle;
        const temp = [
            oldState[edgeCycle[0][0]], 
            oldState[edgeCycle[0][1]], 
            oldState[edgeCycle[0][2]]
        ];

        if (clockwise) {
            // Clockwise cycle: 0->1->2->3->0
            for (let i = 0; i < 3; i++) {
                facelets[edgeCycle[0][i]] = oldState[edgeCycle[3][i]];
                facelets[edgeCycle[3][i]] = oldState[edgeCycle[2][i]];
                facelets[edgeCycle[2][i]] = oldState[edgeCycle[1][i]];
                facelets[edgeCycle[1][i]] = temp[i];
                
                // Record permutations
                permutation.push({ from: edgeCycle[3][i], to: edgeCycle[0][i] });
                permutation.push({ from: edgeCycle[2][i], to: edgeCycle[3][i] });
                permutation.push({ from: edgeCycle[1][i], to: edgeCycle[2][i] });
                permutation.push({ from: edgeCycle[0][i], to: edgeCycle[1][i] });
            }
        } else {
            // Counter-clockwise cycle: 0->3->2->1->0
            for (let i = 0; i < 3; i++) {
                facelets[edgeCycle[0][i]] = oldState[edgeCycle[1][i]];
                facelets[edgeCycle[1][i]] = oldState[edgeCycle[2][i]];
                facelets[edgeCycle[2][i]] = oldState[edgeCycle[3][i]];
                facelets[edgeCycle[3][i]] = temp[i];
                
                // Record permutations
                permutation.push({ from: edgeCycle[1][i], to: edgeCycle[0][i] });
                permutation.push({ from: edgeCycle[2][i], to: edgeCycle[1][i] });
                permutation.push({ from: edgeCycle[3][i], to: edgeCycle[2][i] });
                permutation.push({ from: edgeCycle[0][i], to: edgeCycle[3][i] });
            }
        }

        return permutation;
    }

    executeSequence(sequence, recordHistory = true) {
        /**
         * Execute a sequence of moves
         */
        const moves = this.parseSequence(sequence);
        const allPermutations = [];
        
        console.log(`🎯 Move Engine: Executing sequence: ${sequence}`);
        
        for (const move of moves) {
            const { face, clockwise } = move;
            const permutation = this.executeMove(face, clockwise, recordHistory);
            allPermutations.push(...permutation);
        }
        
        return allPermutations;
    }

    parseSequence(sequence) {
        /**
         * Parse a move sequence string into individual moves
         */
        const moves = [];
        const tokens = sequence.trim().split(/\s+/);
        
        for (const token of tokens) {
            if (token.length === 0) continue;
            
            const face = token[0].toUpperCase();
            const clockwise = !token.includes("'") && !token.includes("'");
            const double = token.includes('2');
            
            if (!['U', 'D', 'F', 'B', 'R', 'L'].includes(face)) {
                throw new Error(`Invalid face in sequence: ${face}`);
            }
            
            if (double) {
                // Double move = two single moves
                moves.push({ face, clockwise: true });
                moves.push({ face, clockwise: true });
            } else {
                moves.push({ face, clockwise });
            }
        }
        
        return moves;
    }

    undo() {
        /**
         * Undo the last move
         */
        if (this.undoStack.length === 0) {
            console.log('⚠️ No moves to undo');
            return false;
        }
        
        const lastState = this.undoStack.pop();
        this.cubeState.facelets = lastState.state.slice();
        
        // Remove from history
        this.moveHistory.pop();
        this.permutationHistory.pop();
        
        console.log(`↩️ Undid move: ${lastState.move}`);
        return true;
    }

    getFaceStartIndex(face) {
        /**
         * Get the starting index for a face in the facelets array
         */
        const faceIndices = { 'U': 0, 'R': 9, 'F': 18, 'D': 27, 'L': 36, 'B': 45 };
        return faceIndices[face];
    }

    invertMapping(mapping) {
        /**
         * Invert a position mapping for counter-clockwise rotation
         */
        const inverted = new Array(mapping.length);
        for (let i = 0; i < mapping.length; i++) {
            inverted[mapping[i]] = i;
        }
        return inverted;
    }

    getMoveHistory() {
        /**
         * Get the complete move history
         */
        return {
            moves: [...this.moveHistory],
            permutations: [...this.permutationHistory],
            totalMoves: this.moveHistory.length
        };
    }

    getPermutationCycles() {
        /**
         * Analyze the current permutation state and find cycles
         */
        const cycles = [];
        const visited = new Set();
        
        // This would require more complex cycle detection algorithm
        // For now, return basic info
        return {
            totalPermutations: this.permutationHistory.length,
            moveCount: this.moveHistory.length,
            lastMoves: this.moveHistory.slice(-10)
        };
    }

    reset() {
        /**
         * Reset the move engine
         */
        this.moveHistory = [];
        this.permutationHistory = [];
        this.undoStack = [];
        console.log('🔄 Move engine reset');
    }

    validateMove(face, clockwise) {
        /**
         * Validate that a move is legal
         */
        if (!['U', 'D', 'F', 'B', 'R', 'L'].includes(face)) {
            return { valid: false, error: `Invalid face: ${face}` };
        }
        
        if (typeof clockwise !== 'boolean') {
            return { valid: false, error: 'Clockwise parameter must be boolean' };
        }
        
        return { valid: true };
    }

    getStatistics() {
        /**
         * Get detailed statistics about moves and permutations
         */
        const faceCounts = {};
        for (const move of this.moveHistory) {
            const face = move[0];
            faceCounts[face] = (faceCounts[face] || 0) + 1;
        }
        
        return {
            totalMoves: this.moveHistory.length,
            faceCounts: faceCounts,
            undoStackSize: this.undoStack.length,
            lastMove: this.moveHistory[this.moveHistory.length - 1] || null,
            canUndo: this.undoStack.length > 0
        };
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.MoveEngine = MoveEngine;
} else {
    module.exports = MoveEngine;
}
