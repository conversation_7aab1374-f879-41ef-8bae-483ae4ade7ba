/**
 * Main Application Controller
 * Coordinates the 3D cube, 2D display, and solver
 */

class CubeApp {
    constructor() {
        this.cubeState = new CubeState();
        this.cube3D = null;
        this.cube2D = null;
        this.solver = new KociembaSolver();
        this.isAnimating = false;
        this.moveCount = 0;
        this.stateDisplayVisible = false;
        this.currentStateTab = 'kociemba';

        this.init();
    }

    async init() {
        console.log('🚀 Initializing Cube Application...');
        
        // Initialize 3D cube
        const cube3DContainer = document.getElementById('cube3d-container');
        if (cube3DContainer) {
            this.cube3D = new Cube3D(cube3DContainer, this.cubeState);
            console.log('✅ 3D cube initialized');
        }

        // Initialize 2D cube
        const cube2DContainer = document.getElementById('cube2d-container');
        if (cube2DContainer) {
            this.cube2D = new Cube2D(cube2DContainer, this.cubeState);
            console.log('✅ 2D cube initialized');
        }

        // Check solver status
        await this.checkSolverStatus();

        // Update display
        this.updateDisplay();

        console.log('🎉 Cube Application ready!');
    }

    async rotateFace(face, clockwise) {
        /**
         * Rotate a face of the cube
         */
        if (this.isAnimating) {
            console.log('⏳ Animation in progress, please wait...');
            return;
        }

        console.log(`🔄 Rotating face ${face}${clockwise ? '' : "'"}`);
        
        try {
            this.isAnimating = true;
            this.setControlsEnabled(false);

            // Animate 3D rotation first
            if (this.cube3D) {
                await this.cube3D.animateFaceRotation(face, clockwise, 300);
            }

            // Update cube state
            this.cubeState.executeMove(face, clockwise);
            this.moveCount++;

            // Update visualizations
            this.refreshVisualization();
            this.updateDisplay();

            // Check if solved
            if (this.cubeState.isSolved()) {
                this.showMessage('🎉 Congratulations! Cube is solved!', 'success');
            }

        } catch (error) {
            console.error('❌ Error rotating face:', error);
            this.showMessage('Error rotating face: ' + error.message, 'error');
        } finally {
            this.isAnimating = false;
            this.setControlsEnabled(true);
        }
    }

    async autoScramble() {
        /**
         * Auto scramble with user-specified length
         */
        if (this.isAnimating) return;

        const lengthInput = document.getElementById('scramble-length');
        const length = lengthInput ? parseInt(lengthInput.value) || 15 : 15;

        console.log(`🎲 Auto scrambling cube with ${length} moves...`);

        try {
            this.isAnimating = true;
            this.setControlsEnabled(false);

            this.showMessage(`🎲 Scrambling cube with ${length} moves...`, 'info');

            // Generate scramble sequence
            const scrambleSequence = this.cubeState.scramble(length);
            console.log('🔀 Scramble sequence:', scrambleSequence);

            // Reset move count since we're scrambling
            this.moveCount = 0;

            // Update visualizations
            this.refreshVisualization();
            this.updateDisplay();

            // Show scramble in solution display
            const solutionDisplay = document.getElementById('solution-display');
            if (solutionDisplay) {
                solutionDisplay.innerHTML = `
                    <strong>🎲 Cube Auto-Scrambled!</strong><br>
                    <strong>Length:</strong> ${length} moves<br>
                    <strong>Sequence:</strong> ${scrambleSequence}<br>
                    <em>Click "Solve" to find the optimal solution.</em>
                `;
            }

            this.showMessage(`🎲 Cube scrambled with ${length} moves!`, 'success');

        } catch (error) {
            console.error('❌ Error scrambling cube:', error);
            this.showMessage('Error scrambling cube: ' + error.message, 'error');
        } finally {
            this.isAnimating = false;
            this.setControlsEnabled(true);
        }
    }

    async customScramble() {
        /**
         * Apply custom scramble sequence
         */
        if (this.isAnimating) return;

        const customInput = document.getElementById('custom-scramble');
        const sequence = customInput ? customInput.value.trim() : '';

        if (!sequence) {
            this.showMessage('⚠️ Please enter a scramble sequence', 'warning');
            return;
        }

        console.log(`📝 Applying custom scramble: ${sequence}`);

        try {
            this.isAnimating = true;
            this.setControlsEnabled(false);

            this.showMessage('📝 Applying custom scramble...', 'info');

            // Apply custom sequence
            this.cubeState.applyMoveSequence(sequence);

            // Reset move count since we're scrambling
            this.moveCount = 0;

            // Update visualizations
            this.refreshVisualization();
            this.updateDisplay();

            // Show scramble in solution display
            const solutionDisplay = document.getElementById('solution-display');
            if (solutionDisplay) {
                solutionDisplay.innerHTML = `
                    <strong>📝 Custom Scramble Applied!</strong><br>
                    <strong>Sequence:</strong> ${sequence}<br>
                    <em>Click "Solve" to find the optimal solution.</em>
                `;
            }

            // Clear the input
            if (customInput) {
                customInput.value = '';
            }

            this.showMessage('📝 Custom scramble applied successfully!', 'success');

        } catch (error) {
            console.error('❌ Error applying custom scramble:', error);
            this.showMessage('Error applying scramble: ' + error.message, 'error');
        } finally {
            this.isAnimating = false;
            this.setControlsEnabled(true);
        }
    }

    async solveCube() {
        /**
         * Solve the cube using Kociemba algorithm
         */
        if (this.isAnimating) return;

        if (this.cubeState.isSolved()) {
            this.showMessage('🎉 Cube is already solved!', 'success');
            return;
        }

        console.log('🧠 Solving cube...');
        
        try {
            this.isAnimating = true;
            this.setControlsEnabled(false);
            
            const solutionDisplay = document.getElementById('solution-display');
            if (solutionDisplay) {
                solutionDisplay.innerHTML = '<strong>🔍 Analyzing cube state...</strong>';
            }

            // Solve using Kociemba algorithm
            const result = await this.solver.solveCube(this.cubeState);

            if (result.success) {
                console.log('✅ Solution found:', result);
                
                if (solutionDisplay) {
                    solutionDisplay.innerHTML = `
                        <div style="background: rgba(76, 175, 80, 0.1); padding: 15px; border-radius: 8px; border: 2px solid #4CAF50;">
                            <strong>✅ Solution Found!</strong><br>
                            <strong>Algorithm:</strong> ${result.algorithm}<br>
                            <strong>Length:</strong> ${result.moves.length} moves<br>
                            <strong>Solution:</strong><br>
                            <div style="background: rgba(0,0,0,0.2); padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0;">
                                ${result.solution}
                            </div>
                            <button onclick="app.applySolution('${result.solution}')" 
                                    style="background: #2196F3; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; font-weight: bold;">
                                🎯 Apply Solution
                            </button>
                        </div>
                    `;
                }

                this.showMessage(`✅ Solution found in ${result.moves.length} moves!`, 'success');

            } else {
                console.error('❌ Solving failed:', result.error);
                
                if (solutionDisplay) {
                    solutionDisplay.innerHTML = `
                        <div style="background: rgba(244, 67, 54, 0.1); padding: 15px; border-radius: 8px; border: 2px solid #f44336;">
                            <strong>❌ Solving Failed</strong><br>
                            <em>${result.error}</em><br><br>
                            <button onclick="app.showSetupInstructions()" 
                                    style="background: #ff9800; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; font-weight: bold;">
                                📋 Setup Instructions
                            </button>
                        </div>
                    `;
                }

                this.showMessage('❌ Solving failed: ' + result.error, 'error');
            }

        } catch (error) {
            console.error('❌ Solving error:', error);
            this.showMessage('Solving error: ' + error.message, 'error');
        } finally {
            this.isAnimating = false;
            this.setControlsEnabled(true);
        }
    }

    async applySolution(solutionString) {
        /**
         * Apply a solution string to the cube
         */
        if (this.isAnimating) return;

        console.log('🎯 Applying solution:', solutionString);
        
        try {
            this.isAnimating = true;
            this.setControlsEnabled(false);

            const moves = solutionString.trim().split(/\s+/).filter(move => move.length > 0);
            
            for (let i = 0; i < moves.length; i++) {
                const move = moves[i];
                const face = move[0];
                const clockwise = !move.includes("'");
                
                console.log(`Applying move ${i + 1}/${moves.length}: ${move}`);
                
                // Animate the move
                if (this.cube3D) {
                    await this.cube3D.animateFaceRotation(face, clockwise, 200);
                }
                
                // Update cube state
                this.cubeState.executeMove(face, clockwise);
                this.moveCount++;
                
                // Update display
                this.refreshVisualization();
                this.updateDisplay();
                
                // Small delay between moves
                await new Promise(resolve => setTimeout(resolve, 50));
            }

            // Check if solved
            if (this.cubeState.isSolved()) {
                this.showMessage('🎉 Solution applied successfully! Cube is solved!', 'success');
                
                const solutionDisplay = document.getElementById('solution-display');
                if (solutionDisplay) {
                    solutionDisplay.innerHTML = `
                        <div style="background: rgba(76, 175, 80, 0.1); padding: 15px; border-radius: 8px; border: 2px solid #4CAF50;">
                            <strong>🎉 Cube Solved Successfully!</strong><br>
                            <strong>Solution applied:</strong> ${solutionString}<br>
                            <em>Great job! The cube is now solved.</em>
                        </div>
                    `;
                }
            } else {
                this.showMessage('⚠️ Solution applied but cube may not be fully solved', 'warning');
            }

        } catch (error) {
            console.error('❌ Error applying solution:', error);
            this.showMessage('Error applying solution: ' + error.message, 'error');
        } finally {
            this.isAnimating = false;
            this.setControlsEnabled(true);
        }
    }

    resetCube() {
        /**
         * Reset cube to solved state
         */
        if (this.isAnimating) return;

        console.log('🔄 Resetting cube...');

        this.cubeState.reset();
        this.moveCount = 0;

        this.refreshVisualization();
        this.updateDisplay();

        const solutionDisplay = document.getElementById('solution-display');
        if (solutionDisplay) {
            solutionDisplay.innerHTML = 'Cube reset to solved state. Use scramble options to mix it up!';
        }

        this.showMessage('🔄 Cube reset to solved state', 'success');
    }

    undoMove() {
        /**
         * Undo the last move
         */
        if (this.isAnimating) return;

        console.log('↩️ Undoing last move...');

        try {
            if (this.cubeState.moveEngine && this.cubeState.moveEngine.undo()) {
                this.moveCount = Math.max(0, this.moveCount - 1);

                this.refreshVisualization();
                this.updateDisplay();

                this.showMessage('↩️ Move undone', 'success');
            } else {
                this.showMessage('⚠️ No moves to undo', 'warning');
            }
        } catch (error) {
            console.error('❌ Error undoing move:', error);
            this.showMessage('Error undoing move: ' + error.message, 'error');
        }
    }

    refreshVisualization() {
        /**
         * Refresh both 3D and 2D visualizations
         */
        if (this.cube3D) {
            this.cube3D.refresh();
        }
        if (this.cube2D) {
            this.cube2D.refresh();
        }
    }

    updateDisplay() {
        /**
         * Update status displays
         */
        // Update cube status
        const cubeStatusElement = document.getElementById('cube-status');
        if (cubeStatusElement) {
            cubeStatusElement.textContent = this.cubeState.isSolved() ? 'Solved ✅' : 'Scrambled 🎲';
        }

        // Update undo button state
        const undoBtn = document.getElementById('undo-btn');
        if (undoBtn && this.cubeState.moveEngine) {
            const stats = this.cubeState.moveEngine.getStatistics();
            undoBtn.disabled = !stats.canUndo || this.isAnimating;
            undoBtn.style.opacity = stats.canUndo && !this.isAnimating ? '1' : '0.6';
        }

        // Update state display if visible
        if (this.stateDisplayVisible) {
            this.updateStateDisplay();
        }
    }

    async checkSolverStatus() {
        /**
         * Check and update solver status
         */
        const solverStatusElement = document.getElementById('solver-status');
        if (!solverStatusElement) return;

        try {
            const isAvailable = await this.solver.checkServer();
            
            if (isAvailable) {
                solverStatusElement.innerHTML = 'Ready ✅';
                solverStatusElement.style.color = '#4CAF50';
            } else {
                solverStatusElement.innerHTML = 'Server Offline ❌';
                solverStatusElement.style.color = '#f44336';
            }
        } catch (error) {
            solverStatusElement.innerHTML = 'Error ⚠️';
            solverStatusElement.style.color = '#ff9800';
        }
    }

    showSetupInstructions() {
        /**
         * Show setup instructions for the Kociemba server
         */
        const instructions = this.solver.getSetupInstructions();
        
        const solutionDisplay = document.getElementById('solution-display');
        if (solutionDisplay) {
            solutionDisplay.innerHTML = `
                <div style="background: rgba(255, 152, 0, 0.1); padding: 15px; border-radius: 8px; border: 2px solid #ff9800;">
                    <strong>📋 Kociemba Server Setup</strong><br>
                    <pre style="background: rgba(0,0,0,0.2); padding: 10px; border-radius: 5px; font-size: 12px; overflow-x: auto; white-space: pre-wrap;">${instructions}</pre>
                    <button onclick="app.checkSolverStatus()" 
                            style="background: #4CAF50; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; font-weight: bold;">
                        🔍 Check Server Status
                    </button>
                </div>
            `;
        }
    }

    setControlsEnabled(enabled) {
        /**
         * Enable or disable all controls
         */
        const buttons = document.querySelectorAll('button');
        buttons.forEach(button => {
            button.disabled = !enabled;
            button.style.opacity = enabled ? '1' : '0.6';
        });
    }

    toggleStateDisplay() {
        /**
         * Toggle the cube state display
         */
        const stateDisplay = document.getElementById('cube-state-display');
        const toggleBtn = document.getElementById('state-toggle-btn');

        if (!stateDisplay || !toggleBtn) return;

        this.stateDisplayVisible = !this.stateDisplayVisible;

        if (this.stateDisplayVisible) {
            stateDisplay.style.display = 'block';
            toggleBtn.textContent = 'Hide State';
            this.updateStateDisplay();
        } else {
            stateDisplay.style.display = 'none';
            toggleBtn.textContent = 'Show State';
        }
    }

    showStateTab(tabName) {
        /**
         * Switch between different state display tabs
         */
        this.currentStateTab = tabName;

        // Update tab buttons
        const tabs = document.querySelectorAll('.state-tab');
        tabs.forEach(tab => {
            tab.classList.remove('active');
            if (tab.textContent.toLowerCase().includes(tabName)) {
                tab.classList.add('active');
            }
        });

        this.updateStateDisplay();
    }

    updateStateDisplay() {
        /**
         * Update the cube state display content
         */
        if (!this.stateDisplayVisible) return;

        const stateContent = document.getElementById('state-content');
        if (!stateContent) return;

        switch (this.currentStateTab) {
            case 'kociemba':
                stateContent.innerHTML = this.generateKociembaDisplay();
                break;
            case 'faces':
                stateContent.innerHTML = this.generateFacesDisplay();
                break;
            case 'positions':
                stateContent.innerHTML = this.generatePositionsDisplay();
                break;
        }
    }

    generateKociembaDisplay() {
        /**
         * Generate the Kociemba string display
         */
        const cubeString = this.cubeState.toString();
        const faces = ['U', 'R', 'F', 'D', 'L', 'B'];

        let html = '<div class="kociemba-string">';
        html += '<h4>📋 Kociemba Format String (54 characters)</h4>';
        html += '<div style="margin: 15px 0; font-size: 16px; font-weight: bold;">';

        // Color-code each section
        for (let i = 0; i < 6; i++) {
            const faceString = cubeString.substr(i * 9, 9);
            const faceName = faces[i];
            const color = this.getFaceDisplayColor(faceName);

            html += `<span style="color: ${color}; margin-right: 10px;" title="${faceName} face">${faceString}</span>`;
            if (i === 2) html += '<br>'; // Line break after F
        }

        html += '</div>';
        html += '<div style="font-size: 12px; opacity: 0.8;">';
        html += 'Order: U(0-8), R(9-17), F(18-26), D(27-35), L(36-44), B(45-53)<br>';
        html += 'Colors: U=White, R=Red, F=Green, D=Yellow, L=Orange, B=Blue';
        html += '</div>';
        html += '</div>';

        return html;
    }

    generateFacesDisplay() {
        /**
         * Generate the face arrays display
         */
        const faces = this.cubeState.getFaces();
        const faceNames = ['U', 'R', 'F', 'D', 'L', 'B'];
        const faceColors = {
            'U': '#ffffff', 'R': '#ff0000', 'F': '#00ff00',
            'D': '#ffff00', 'L': '#ff8000', 'B': '#0000ff'
        };

        let html = '<div>';

        for (const faceName of faceNames) {
            const face = faces[faceName];
            const color = faceColors[faceName];

            html += `<div class="face-array">`;
            html += `<h4 style="color: ${color};">${faceName} Face (${this.getFaceFullName(faceName)})</h4>`;
            html += `<div class="face-grid">`;

            for (let row = 0; row < 3; row++) {
                for (let col = 0; col < 3; col++) {
                    const facelet = face[row][col];
                    const cellColor = faceColors[facelet] || '#666';
                    html += `<div class="face-cell" style="background-color: ${cellColor}; color: ${cellColor === '#ffff00' ? '#000' : '#fff'};" title="Position [${row}][${col}]">${facelet}</div>`;
                }
            }

            html += `</div></div>`;
        }

        html += '</div>';
        return html;
    }

    generatePositionsDisplay() {
        /**
         * Generate the position mapping display
         */
        const facelets = this.cubeState.facelets;
        const faces = ['U', 'R', 'F', 'D', 'L', 'B'];

        let html = '<div class="position-map">';

        for (let f = 0; f < 6; f++) {
            const faceName = faces[f];
            const startPos = f * 9;
            const color = this.getFaceDisplayColor(faceName);

            html += `<div class="position-group">`;
            html += `<h5 style="color: ${color};">${faceName} (${startPos}-${startPos + 8})</h5>`;

            for (let i = 0; i < 9; i++) {
                const pos = startPos + i;
                const facelet = facelets[pos];
                const row = Math.floor(i / 3);
                const col = i % 3;

                html += `<div style="font-size: 11px; margin: 2px 0; padding: 2px; background: rgba(0,0,0,0.3); border-radius: 3px;">`;
                html += `${pos}: ${facelet} [${row},${col}]`;
                html += `</div>`;
            }

            html += `</div>`;
        }

        html += '</div>';
        return html;
    }

    getFaceDisplayColor(faceName) {
        /**
         * Get display color for face names
         */
        const colors = {
            'U': '#ffffff', 'R': '#ff4444', 'F': '#44ff44',
            'D': '#ffff44', 'L': '#ff8844', 'B': '#4444ff'
        };
        return colors[faceName] || '#ffffff';
    }

    getFaceFullName(faceName) {
        /**
         * Get full name for face
         */
        const names = {
            'U': 'Up/White', 'R': 'Right/Red', 'F': 'Front/Green',
            'D': 'Down/Yellow', 'L': 'Left/Orange', 'B': 'Back/Blue'
        };
        return names[faceName] || faceName;
    }

    debug3DMapping() {
        /**
         * Debug 3D cube mapping by opening debug tool
         */
        const debugWindow = window.open('debug-3d.html', '_blank', 'width=1200,height=800');
        if (debugWindow) {
            this.showMessage('🔍 3D Debug tool opened in new window', 'info');
        } else {
            this.showMessage('⚠️ Please allow popups to open debug tool', 'warning');
        }
    }

    showMessage(message, type = 'info') {
        /**
         * Show a temporary message to the user
         */
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;

        document.body.appendChild(messageDiv);

        // Auto-remove after 4 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }
        }, 4000);
    }
}

// Initialize the application when the page loads
let app;
window.addEventListener('DOMContentLoaded', () => {
    app = new CubeApp();
    window.app = app; // Make it globally accessible for button clicks
});
