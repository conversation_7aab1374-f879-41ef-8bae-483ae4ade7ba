#!/usr/bin/env python3
"""
3x3 Cube Project Startup Script
Starts both the Kociemba solver server and web server
"""

import os
import sys
import time
import threading
import subprocess
import webbrowser
from http.server import HTTPServer, SimpleHTTPRequestHandler
import socket

def check_port_available(port):
    """Check if a port is available"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            return True
        except OSError:
            return False

def start_kociemba_server(port=8081):
    """Start the Kociemba solver server"""
    print(f"🧩 Starting Kociemba solver server on port {port}...")
    
    # Check if RubiksCube-TwophaseSolver-master exists
    solver_path = '../RubiksCube-TwophaseSolver-master'
    if not os.path.exists(solver_path):
        print(f"❌ Error: {solver_path} directory not found!")
        print("Please ensure the RubiksCube-TwophaseSolver-master folder is in the parent directory.")
        return None
    
    # Check if port is available
    if not check_port_available(port):
        print(f"❌ Port {port} is already in use!")
        return None
    
    try:
        # Start the Kociemba server
        process = subprocess.Popen([
            sys.executable, 'kociemba-server.py', str(port)
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        print(f"✅ Kociemba server started (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"❌ Failed to start Kociemba server: {e}")
        return None

def start_web_server(port=3000):
    """Start the web server for the cube interface"""
    print(f"🌐 Starting web server on port {port}...")
    
    # Check if port is available
    if not check_port_available(port):
        print(f"❌ Port {port} is already in use!")
        return None
    
    try:
        # Custom handler to serve files with proper MIME types
        class CubeHTTPRequestHandler(SimpleHTTPRequestHandler):
            def end_headers(self):
                self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
                self.send_header('Pragma', 'no-cache')
                self.send_header('Expires', '0')
                super().end_headers()
            
            def log_message(self, format, *args):
                # Suppress default logging
                pass
        
        server = HTTPServer(('localhost', port), CubeHTTPRequestHandler)
        
        def run_server():
            print(f"✅ Web server running at http://localhost:{port}")
            server.serve_forever()
        
        # Start server in a separate thread
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        return server
        
    except Exception as e:
        print(f"❌ Failed to start web server: {e}")
        return None

def main():
    """Main startup function"""
    print("🚀 Starting 3x3 Cube Project...")
    print("=" * 50)
    
    # Start Kociemba server
    kociemba_process = start_kociemba_server(8081)
    if not kociemba_process:
        print("⚠️ Continuing without Kociemba solver...")
    
    # Start web server
    web_server = start_web_server(3000)
    if not web_server:
        print("❌ Failed to start web server. Exiting.")
        if kociemba_process:
            kociemba_process.terminate()
        return
    
    # Wait a moment for servers to start
    time.sleep(2)
    
    # Open browser
    print("🌐 Opening browser...")
    try:
        webbrowser.open('http://localhost:3000')
    except Exception as e:
        print(f"⚠️ Could not open browser automatically: {e}")
        print("Please open http://localhost:3000 manually")
    
    print("\n" + "=" * 50)
    print("🎉 3x3 Cube Project is running!")
    print("📋 Available URLs:")
    print("   🧩 Main Interface: http://localhost:3000")
    
    if kociemba_process:
        print("   🤖 Kociemba API:   http://localhost:8081/health")
    
    print("\n💡 Tips:")
    print("   - Use Ctrl+C to stop all servers")
    print("   - First Kociemba run may take time to generate lookup tables")
    print("   - Check console for any error messages")
    print("   - Try scrambling the cube and then solving it!")
    print("=" * 50)
    
    try:
        # Keep the main thread alive
        while True:
            time.sleep(1)
            
            # Check if Kociemba process is still running
            if kociemba_process and kociemba_process.poll() is not None:
                print("⚠️ Kociemba server process terminated")
                kociemba_process = None
    
    except KeyboardInterrupt:
        print("\n🛑 Shutting down servers...")
        
        # Stop web server
        if web_server:
            web_server.shutdown()
            print("✅ Web server stopped")
        
        # Stop Kociemba server
        if kociemba_process:
            kociemba_process.terminate()
            try:
                kociemba_process.wait(timeout=5)
                print("✅ Kociemba server stopped")
            except subprocess.TimeoutExpired:
                kociemba_process.kill()
                print("⚠️ Kociemba server force-killed")
        
        print("👋 3x3 Cube Project stopped. Goodbye!")

if __name__ == '__main__':
    main()
