# 3x3 <PERSON><PERSON><PERSON>'s Cube Solver

A complete 3x3 Rubik's cube implementation with 3D visualization and optimal solving using the Kociemba two-phase algorithm.

## Features

🧩 **Interactive 3D Cube**
- Realistic 3D visualization using Three.js
- Fixed: No more cube deformation during moves - only colors change
- Mouse controls for rotation and zoom
- Smooth visual transitions

📋 **Enhanced 2D Net Display**
- Traditional flat cube layout with clear face labels (U, R, F, D, L, B)
- Real-time synchronization with 3D view
- Color-coded faces with hover effects
- Position tooltips for debugging

🧠 **Optimal Solving**
- Kociemba two-phase algorithm integration
- Solutions typically 15-20 moves
- Sub-second solving for most scrambles

🎲 **Advanced Scrambling**
- **Auto Scramble**: User-selectable length (5-50 moves)
- **Custom Scramble**: Enter your own move sequences
- **Smart Parsing**: Handles standard notation (R, U', F2, etc.)

🔧 **Professional Move Engine**
- Complete permutation tracking
- Undo/Redo functionality
- Move history and statistics
- Sequence validation and parsing
- Performance analytics

⚡ **Enhanced Controls**
- Face rotation controls (U, R, F, D, L, B and primes)
- Undo last move functionality
- Reset to solved state
- Real-time move counting

## Quick Start

### Prerequisites
- Python 3.6+
- Modern web browser
- RubiksCube-TwophaseSolver-master folder

### Setup

1. **Download the Kociemba solver:**
   ```bash
   # Place RubiksCube-TwophaseSolver-master in the parent directory
   # Your structure should look like:
   # parent-folder/
   #   ├── cube3d-project/
   #   └── RubiksCube-TwophaseSolver-master/
   ```

2. **Start the application:**
   ```bash
   cd cube3d-project
   python start-project.py
   ```

3. **Open your browser:**
   - The application will automatically open at `http://localhost:3000`
   - If not, navigate there manually

### Manual Setup (Alternative)

1. **Start the Kociemba server:**
   ```bash
   python kociemba-server.py 8081
   ```

2. **Start the web server:**
   ```bash
   python -m http.server 3000
   ```

3. **Open `http://localhost:3000`**

## Usage

### Basic Controls

**Face Rotations:**
- Click face buttons (U, R, F, D, L, B) for clockwise rotations
- Click prime buttons (U', R', F', D', L', B') for counter-clockwise

**Actions:**
- 🎲 **Scramble**: Randomly mix up the cube
- 🧠 **Solve**: Find optimal solution using Kociemba algorithm
- 🔄 **Reset**: Return to solved state

**3D View:**
- **Mouse drag**: Rotate the cube view
- **Mouse wheel**: Zoom in/out

### Solving Process

1. **Scramble the cube** using the Scramble button
2. **Click Solve** to find the optimal solution
3. **Apply the solution** by clicking the "Apply Solution" button
4. **Watch** as the cube solves itself with smooth animations

## Technical Details

### Architecture

- **Frontend**: Pure JavaScript with Three.js for 3D rendering
- **Backend**: Python HTTP server wrapping the Kociemba solver
- **Algorithm**: Herbert Kociemba's two-phase algorithm
- **Communication**: REST API between frontend and solver

### Cube Representation

The cube uses the standard Kociemba format:
- **54 characters** representing all facelets
- **Order**: U(0-8), R(9-17), F(18-26), D(27-35), L(36-44), B(45-53)
- **Colors**: U=White, R=Red, F=Green, D=Yellow, L=Orange, B=Blue

### Performance

- **Solving Time**: Usually < 1 second
- **Solution Length**: Typically 15-20 moves (optimal)
- **Animation**: Smooth 60fps animations
- **First Run**: May take 30+ minutes to generate lookup tables

## File Structure

```
cube3d-project/
├── index.html              # Main HTML page
├── js/
│   ├── app.js              # Main application controller
│   ├── cube-state.js       # Cube state management
│   ├── cube-3d.js          # 3D visualization
│   ├── cube-2d.js          # 2D net display
│   └── kociemba-solver.js  # Solver interface
├── kociemba-server.py      # Python solver server
├── start-project.py        # Startup script
└── README.md               # This file
```

## Troubleshooting

### Common Issues

**"Kociemba server not available"**
- Ensure RubiksCube-TwophaseSolver-master is in the parent directory
- Check that Python can import the solver module
- Verify port 8080 is not in use

**Slow first run**
- Normal behavior - generating lookup tables
- Wait for "Server is ready" message
- Subsequent runs will be fast

**CORS errors**
- Use `http://localhost:3000` not `file://`
- Ensure both servers are running
- Check browser console for details

**Animation issues**
- Ensure modern browser with WebGL support
- Check browser console for Three.js errors
- Try refreshing the page

### Debug Mode

Enable detailed logging in browser console:
```javascript
// In browser console
app.solver.debug = true;
```

## Development

### Adding New Features

1. **New algorithms**: Extend `kociemba-solver.js`
2. **UI improvements**: Modify `index.html` and CSS
3. **Visualization**: Update `cube-3d.js` or `cube-2d.js`
4. **Controls**: Extend `app.js`

### Testing

- Test with various scrambles
- Verify solutions are correct
- Check animation smoothness
- Test server connectivity

## Credits

- **Herbert Kociemba**: Original two-phase algorithm
- **RubiksCube-TwophaseSolver-master**: Python implementation
- **Three.js**: 3D graphics library
- **Clean Cube Project**: This implementation

## License

This project builds upon the RubiksCube-TwophaseSolver-master implementation. Please respect the original license terms.

---

**Enjoy solving cubes! 🧩✨**
