<!DOCTYPE html>
<html>
<head>
    <title>Clean Cube Test - Working Implementation</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .result { 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 8px; 
            border-left: 4px solid;
        }
        .success { 
            background-color: rgba(40, 167, 69, 0.2); 
            color: #28a745; 
            border-left-color: #28a745;
        }
        .error { 
            background-color: rgba(220, 53, 69, 0.2); 
            color: #dc3545; 
            border-left-color: #dc3545;
        }
        .info { 
            background-color: rgba(23, 162, 184, 0.2); 
            color: #17a2b8; 
            border-left-color: #17a2b8;
        }
        .warning { 
            background-color: rgba(255, 193, 7, 0.2); 
            color: #ffc107; 
            border-left-color: #ffc107;
        }
        button { 
            margin: 10px 5px; 
            padding: 12px 20px; 
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white; 
            border: none; 
            border-radius: 25px; 
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .test-section { 
            margin: 30px 0; 
            padding: 20px; 
            border: 1px solid rgba(255,255,255,0.2); 
            border-radius: 10px;
            background: rgba(255,255,255,0.05);
        }
        pre { 
            background: rgba(0,0,0,0.3); 
            padding: 15px; 
            border-radius: 8px; 
            overflow-x: auto;
            color: #f8f9fa;
        }
        h1, h2 { text-align: center; margin-bottom: 30px; }
        h1 { font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .cube-display {
            font-family: monospace;
            font-size: 14px;
            background: rgba(0,0,0,0.4);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎲 Clean Cube Implementation Test</h1>
        
        <div class="test-section">
            <h2>🔧 Step 1: Basic Functionality</h2>
            <button onclick="testBasicFunctionality()">Test Basic Functions</button>
            <div id="basic-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🎲 Step 2: Scramble Test</h2>
            <button onclick="testScramble()">Test Scramble Generation</button>
            <div id="scramble-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🧩 Step 3: Complete Solve Test</h2>
            <button onclick="testCompleteSolve()">Test Scramble & Solve</button>
            <div id="solve-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🎮 Interactive Cube</h2>
            <button onclick="resetCube()">Reset Cube</button>
            <button onclick="scrambleCube()">Scramble</button>
            <button onclick="solveCube()">Solve</button>
            <div id="interactive-results"></div>
            <div id="cube-display" class="cube-display"></div>
        </div>
    </div>

    <script src="cube-solver-clean.js"></script>
    <script>
        let interactiveCube = new CleanCube();
        
        function log(elementId, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            document.getElementById(elementId).appendChild(div);
        }

        function clearResults(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        function updateCubeDisplay() {
            const display = document.getElementById('cube-display');
            const kociemba = interactiveCube.getKociembaString();
            const visual = interactiveCube.getDisplayString();
            
            display.innerHTML = `
                <strong>Kociemba Format:</strong><br>
                ${kociemba}<br><br>
                <strong>Visual Format:</strong><br>
                ${visual}<br><br>
                <strong>Status:</strong> ${interactiveCube.isSolved() ? '✅ SOLVED' : '🔄 SCRAMBLED'}<br>
                <strong>Valid:</strong> ${interactiveCube.isValidState() ? '✅ YES' : '❌ NO'}
            `;
        }

        function testBasicFunctionality() {
            const resultId = 'basic-results';
            clearResults(resultId);
            log(resultId, '🔧 Testing basic cube functionality...', 'info');

            try {
                const cube = new CleanCube();
                
                // Test initial state
                if (cube.isSolved() && cube.isValidState()) {
                    log(resultId, '✅ Initial state: Solved and valid', 'success');
                } else {
                    log(resultId, '❌ Initial state invalid', 'error');
                    return;
                }
                
                // Test single moves
                const moves = ['R', 'U', 'F', 'L', 'D', 'B'];
                for (const move of moves) {
                    cube.reset();
                    cube.applyMoves(move);
                    
                    if (!cube.isSolved() && cube.isValidState()) {
                        log(resultId, `✅ Move ${move}: Changed state, still valid`, 'success');
                    } else {
                        log(resultId, `❌ Move ${move}: Invalid result`, 'error');
                        return;
                    }
                    
                    // Test inverse
                    cube.applyMoves(move + "'");
                    if (cube.isSolved() && cube.isValidState()) {
                        log(resultId, `✅ Move ${move}': Returned to solved`, 'success');
                    } else {
                        log(resultId, `❌ Move ${move}': Did not return to solved`, 'error');
                        return;
                    }
                }
                
                log(resultId, '🎉 All basic functionality tests passed!', 'success');
                
            } catch (error) {
                log(resultId, `❌ Error: ${error.message}`, 'error');
            }
        }

        function testScramble() {
            const resultId = 'scramble-results';
            clearResults(resultId);
            log(resultId, '🎲 Testing scramble generation...', 'info');

            try {
                const cube = new CleanCube();
                
                // Generate scramble
                const scramble = cube.generateScramble(15);
                log(resultId, `Generated scramble: <strong>${scramble}</strong>`, 'info');
                
                // Apply scramble
                cube.applyMoves(scramble);
                
                if (!cube.isSolved() && cube.isValidState()) {
                    log(resultId, '✅ Scramble applied successfully', 'success');
                    log(resultId, `Kociemba: <code>${cube.getKociembaString()}</code>`, 'info');
                } else {
                    log(resultId, '❌ Scramble failed or cube still solved', 'error');
                    return;
                }
                
                log(resultId, '🎉 Scramble test passed!', 'success');
                
            } catch (error) {
                log(resultId, `❌ Error: ${error.message}`, 'error');
            }
        }

        async function testCompleteSolve() {
            const resultId = 'solve-results';
            clearResults(resultId);
            log(resultId, '🧩 Testing complete scramble and solve...', 'info');

            try {
                const cube = new CleanCube();
                
                // Apply a known scramble
                const scramble = 'R U R\' U\' R U R\' U\'';
                log(resultId, `Applying scramble: <strong>${scramble}</strong>`, 'info');
                
                cube.applyMoves(scramble);
                
                if (!cube.isValidState()) {
                    log(resultId, '❌ Invalid cube state after scramble', 'error');
                    return;
                }
                
                log(resultId, '✅ Scramble applied, cube state is valid', 'success');
                
                // Get Kociemba string
                const kociembaString = cube.getKociembaString();
                log(resultId, `Kociemba: <code>${kociembaString}</code>`, 'info');
                
                // Send to solver
                log(resultId, '📡 Sending to Kociemba solver...', 'info');
                
                const response = await fetch('http://localhost:5000/solve', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cube_string: kociembaString })
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(resultId, `❌ Server error: ${errorText}`, 'error');
                    return;
                }
                
                const data = await response.json();
                
                if (data.error) {
                    log(resultId, `❌ Solver error: ${data.error}`, 'error');
                    return;
                }
                
                log(resultId, `✅ Solution received: <strong>${data.solution}</strong>`, 'success');
                log(resultId, `Move count: ${data.move_count}`, 'info');
                
                // Apply solution
                cube.applyMoves(data.solution);
                
                if (cube.isSolved()) {
                    log(resultId, '🎉 SUCCESS! Cube solved perfectly!', 'success');
                } else {
                    log(resultId, '❌ Solution did not solve the cube', 'error');
                }
                
            } catch (error) {
                log(resultId, `❌ Error: ${error.message}`, 'error');
            }
        }

        function resetCube() {
            interactiveCube.reset();
            log('interactive-results', '🔄 Cube reset to solved state', 'info');
            updateCubeDisplay();
        }

        function scrambleCube() {
            const scramble = interactiveCube.generateScramble(20);
            interactiveCube.applyMoves(scramble);
            log('interactive-results', `🎲 Applied scramble: <strong>${scramble}</strong>`, 'info');
            updateCubeDisplay();
        }

        async function solveCube() {
            if (interactiveCube.isSolved()) {
                log('interactive-results', '✅ Cube is already solved!', 'success');
                return;
            }

            try {
                const kociembaString = interactiveCube.getKociembaString();
                log('interactive-results', '📡 Sending to solver...', 'info');
                
                const response = await fetch('http://localhost:5000/solve', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cube_string: kociembaString })
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log('interactive-results', `❌ Server error: ${errorText}`, 'error');
                    return;
                }
                
                const data = await response.json();
                
                if (data.error) {
                    log('interactive-results', `❌ Solver error: ${data.error}`, 'error');
                    return;
                }
                
                log('interactive-results', `✅ Solution: <strong>${data.solution}</strong>`, 'success');
                
                // Apply solution
                interactiveCube.applyMoves(data.solution);
                updateCubeDisplay();
                
                if (interactiveCube.isSolved()) {
                    log('interactive-results', '🎉 Cube solved successfully!', 'success');
                } else {
                    log('interactive-results', '❌ Solution did not work', 'error');
                }
                
            } catch (error) {
                log('interactive-results', `❌ Error: ${error.message}`, 'error');
            }
        }

        // Initialize display
        updateCubeDisplay();
    </script>
</body>
</html>
