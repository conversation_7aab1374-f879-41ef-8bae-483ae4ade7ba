<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Solve Feature</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #222; color: white; }
        .test-section { margin: 20px 0; padding: 15px; background: #333; border-radius: 5px; }
        .test-btn { padding: 10px 20px; margin: 5px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .test-btn:hover { background: #45a049; }
        .error { color: #ff6b6b; }
        .success { color: #51cf66; }
        .info { color: #74c0fc; }
        #console-output { background: #111; padding: 10px; border-radius: 5px; font-family: monospace; max-height: 400px; overflow-y: auto; }
        .cube-display { background: #444; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Cube Solve Test</h1>
    
    <div class="test-section">
        <h3>Test Controls</h3>
        <button class="test-btn" onclick="initCube()">Initialize Cube</button>
        <button class="test-btn" onclick="testMoves()">Test Basic Moves</button>
        <button class="test-btn" onclick="testScrambleAndSolve()">Test Scramble & Solve</button>
        <button class="test-btn" onclick="clearConsole()">Clear Console</button>
    </div>
    
    <div class="test-section">
        <h3>Cube State</h3>
        <div id="cube-display" class="cube-display">Not initialized</div>
    </div>
    
    <div class="test-section">
        <h3>Console Output</h3>
        <div id="console-output"></div>
    </div>

    <!-- Include the cube scripts -->
    <script src="js/cube-state.js"></script>
    
    <script>
        let cubeState;
        const output = document.getElementById('console-output');
        const cubeDisplay = document.getElementById('cube-display');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            output.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            output.scrollTop = output.scrollHeight;
        }
        
        function clearConsole() {
            output.innerHTML = '';
        }
        
        function updateCubeDisplay() {
            if (cubeState) {
                cubeDisplay.innerHTML = `
                    <strong>Cube String:</strong> ${cubeState.cubeString}<br>
                    <strong>Length:</strong> ${cubeState.cubeString.length}<br>
                    <strong>Valid:</strong> ${cubeState.validateCubeState()}<br>
                    <strong>Solved:</strong> ${cubeState.isSolved()}<br>
                    <strong>Kociemba:</strong> ${cubeState.getKociembaString() || 'N/A'}
                `;
            }
        }
        
        function initCube() {
            try {
                log('Initializing 3x3 cube...', 'info');
                cubeState = new CubeState(3);
                log('✓ Cube initialized successfully', 'success');
                updateCubeDisplay();
                
            } catch (error) {
                log(`✗ Error: ${error.message}`, 'error');
                console.error(error);
            }
        }
        
        function testMoves() {
            if (!cubeState) {
                log('Please initialize cube first!', 'error');
                return;
            }
            
            try {
                log('Testing basic moves: R U R\' U\'', 'info');
                
                const moves = ['R', 'U', 'R\'', 'U\''];
                for (const move of moves) {
                    const face = move[0];
                    const clockwise = !move.includes('\'');
                    
                    log(`Applying move: ${move}`, 'info');
                    cubeState.executeMove(face, clockwise);
                    updateCubeDisplay();
                    
                    if (!cubeState.validateCubeState()) {
                        log(`✗ Cube became invalid after move ${move}!`, 'error');
                        return;
                    }
                }
                
                log('✓ All moves applied successfully', 'success');
                
            } catch (error) {
                log(`✗ Error: ${error.message}`, 'error');
                console.error(error);
            }
        }
        
        async function testScrambleAndSolve() {
            if (!cubeState) {
                log('Please initialize cube first!', 'error');
                return;
            }
            
            try {
                // Reset to solved state
                cubeState.resetToSolved();
                log('Reset cube to solved state', 'info');
                updateCubeDisplay();
                
                // Apply a simple scramble
                const scramble = 'R U R\' U\' F R F\' U R U\' R\'';
                log(`Applying scramble: ${scramble}`, 'info');
                
                const moves = scramble.split(' ');
                for (const move of moves) {
                    const face = move[0];
                    const clockwise = !move.includes('\'');
                    cubeState.executeMove(face, clockwise);
                }
                
                log('✓ Scramble applied', 'success');
                updateCubeDisplay();
                
                if (!cubeState.validateCubeState()) {
                    log('✗ Cube state is invalid after scramble!', 'error');
                    return;
                }
                
                // Try to get Kociemba string
                const kociembaString = cubeState.getKociembaString();
                if (!kociembaString) {
                    log('✗ Cannot get Kociemba string - cube orientation invalid', 'error');
                    return;
                }
                
                log(`Kociemba string: ${kociembaString}`, 'info');
                
                // Try to solve via server
                log('Sending solve request to server...', 'info');
                
                const response = await fetch('http://localhost:5000/solve', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ cube_string: kociembaString })
                });
                
                if (!response.ok) {
                    throw new Error(`Server error: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.error) {
                    log(`✗ Solver error: ${data.error}`, 'error');
                    return;
                }
                
                const solution = data.solution || '';
                log(`✓ Solution received: ${solution}`, 'success');
                
                if (solution.trim()) {
                    // Apply solution
                    log('Applying solution...', 'info');
                    const solutionMoves = solution.split(' ');
                    for (const move of solutionMoves) {
                        const face = move[0];
                        const clockwise = !move.includes('\'');
                        cubeState.executeMove(face, clockwise);
                    }
                    
                    updateCubeDisplay();
                    
                    if (cubeState.isSolved()) {
                        log('🎉 Cube solved successfully!', 'success');
                    } else {
                        log('✗ Solution did not solve the cube', 'error');
                    }
                } else {
                    log('Empty solution received', 'info');
                }
                
            } catch (error) {
                log(`✗ Error: ${error.message}`, 'error');
                console.error(error);
            }
        }
        
        // Override console methods to capture output
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            log(args.join(' '), 'info');
        };
        
        const originalError = console.error;
        console.error = function(...args) {
            originalError.apply(console, args);
            log(args.join(' '), 'error');
        };
        
        log('Test console ready. Click "Initialize Cube" to start.', 'success');
    </script>
</body>
</html>
