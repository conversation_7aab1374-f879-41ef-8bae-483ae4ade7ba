// Debug script to analyze cube moves step by step

// Import the test cube class
const fs = require('fs');

// Load and execute the test cube code
const cubeCode = fs.readFileSync('verify_cube_mechanics.js', 'utf8');
eval(cubeCode);

function debugMove(cube, move, clockwise) {
    console.log(`\n--- Executing ${move}${clockwise ? '' : "'"} ---`);
    
    const beforeState = cube.cubeString;
    const beforeFaces = cube.stringToFaces();
    
    console.log('Before move:');
    printCubeState(beforeFaces);
    
    cube.executeMove(move, clockwise);
    
    const afterState = cube.cubeString;
    const afterFaces = cube.stringToFaces();
    
    console.log('After move:');
    printCubeState(afterFaces);
    
    console.log(`State changed: ${beforeState !== afterState}`);
    
    return afterState;
}

function printCubeState(faces) {
    console.log('U face:', faces.U.map(row => row.map(c => c[0]).join('')).join(' '));
    console.log('R face:', faces.R.map(row => row.map(c => c[0]).join('')).join(' '));
    console.log('F face:', faces.F.map(row => row.map(c => c[0]).join('')).join(' '));
    console.log('D face:', faces.D.map(row => row.map(c => c[0]).join('')).join(' '));
    console.log('L face:', faces.L.map(row => row.map(c => c[0]).join('')).join(' '));
    console.log('B face:', faces.B.map(row => row.map(c => c[0]).join('')).join(' '));
}

function debugRURUPrime() {
    console.log('=== DEBUGGING R U R\' U\' SEQUENCE ===');
    
    const cube = new TestCubeState(3);
    const initialState = cube.cubeString;
    
    console.log('Initial solved state:');
    printCubeState(cube.stringToFaces());
    
    // R move
    debugMove(cube, 'R', true);
    
    // U move  
    debugMove(cube, 'U', true);
    
    // R' move
    debugMove(cube, 'R', false);
    
    // U' move
    debugMove(cube, 'U', false);
    
    const finalState = cube.cubeString;
    
    console.log('\n=== FINAL COMPARISON ===');
    console.log('Initial:', initialState);
    console.log('Final:  ', finalState);
    console.log('Match:  ', initialState === finalState);
    
    if (initialState !== finalState) {
        console.log('\n=== DIFFERENCES ===');
        for (let i = 0; i < initialState.length; i++) {
            if (initialState[i] !== finalState[i]) {
                const face = Math.floor(i / 9);
                const pos = i % 9;
                const faceNames = ['U', 'R', 'F', 'D', 'L', 'B'];
                console.log(`Position ${i} (${faceNames[face]} face, pos ${pos}): ${initialState[i]} → ${finalState[i]}`);
            }
        }
    }
}

function testSimpleR() {
    console.log('\n=== TESTING SIMPLE R MOVE ===');
    
    const cube = new TestCubeState(3);
    const initial = cube.stringToFaces();
    
    console.log('Before R move:');
    console.log('U right column:', initial.U.map(row => row[2]));
    console.log('F right column:', initial.F.map(row => row[2]));
    console.log('D right column:', initial.D.map(row => row[2]));
    console.log('B left column:', initial.B.map(row => row[0]));
    
    cube.executeMove('R', true);
    const after = cube.stringToFaces();
    
    console.log('After R move:');
    console.log('U right column:', after.U.map(row => row[2]));
    console.log('F right column:', after.F.map(row => row[2]));
    console.log('D right column:', after.D.map(row => row[2]));
    console.log('B left column:', after.B.map(row => row[0]));
    
    // Expected: U→B(reversed), F→U, D→F, B(reversed)→D
    console.log('\nExpected cycle for R move:');
    console.log('U right → B left (reversed)');
    console.log('F right → U right');
    console.log('D right → F right');
    console.log('B left (reversed) → D right');
}

// Run the debug
debugRURUPrime();
testSimpleR();
