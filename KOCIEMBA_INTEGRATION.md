# Kociemba Two-Phase Algorithm Integration

## Overview

This document describes the integration of the Kociemba two-phase algorithm into our clean cube project. The Kociemba algorithm is one of the most efficient methods for solving 3x3 Rubik's cubes, typically finding solutions in 20 moves or fewer.

## Architecture

### Components Implemented

1. **`js/kociemba-enums.js`** - Core enums and constants
2. **`js/kociemba-face.js`** - FaceCube class for cube representation
3. **`js/kociemba-solver.js`** - Main solver interface with server communication
4. **`js/solver.js`** - Enhanced solver with Kociemba integration
5. **`kociemba-server.py`** - Python HTTP server for the Kociemba algorithm

### Integration Points

- **Cube State Conversion**: Converts between our cube format and Kociemba format
- **Solver Interface**: Unified solving interface that prefers Kociemba for 3x3 cubes
- **Server Communication**: HTTP-based communication with Python solver
- **Error Handling**: Robust fallback mechanisms when server is unavailable

## Kociemba Format

The Kociemba algorithm uses a specific cube representation:

### Cube String Format
- **Length**: 54 characters (6 faces × 9 stickers each)
- **Order**: U(0-8), R(9-17), F(18-26), D(27-35), L(36-44), B(45-53)
- **Characters**: U, R, F, D, L, B (representing face colors)
- **Example**: `UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB` (solved cube)

### Face Mapping
```
Our Format → Kociemba Format
White      → U (Up)
Red        → R (Right)
Green      → F (Front)
Yellow     → D (Down)
Orange     → L (Left)
Blue       → B (Back)
```

### Facelet Positions
Each face is numbered 0-8 in this pattern:
```
0 1 2
3 4 5
6 7 8
```

## Setup Instructions

### 1. Python Server Setup

#### Option A: Using Our Server Script
```bash
# Ensure RubiksCube-TwophaseSolver-master is in the project directory
python kociemba-server.py 8080
```

#### Option B: Using Original Solver
```bash
cd RubiksCube-TwophaseSolver-master
python start_server.py 8080 20 3
```

### 2. First Run Considerations
- **Initial Setup**: First run may take 30+ minutes to generate lookup tables
- **Disk Space**: Lookup tables require ~100MB of disk space
- **Memory**: Server uses ~50MB RAM during operation

### 3. Server Endpoints

#### Health Check
```
GET http://localhost:8080/health
```
Returns server status and availability.

#### Solve Cube
```
GET http://localhost:8080/{54-character-cube-string}
```
Returns solution string or error message.

## Usage Examples

### Basic Solving
```javascript
// Create solver instance
const solver = new KociembaSolver();

// Check if server is available
const isAvailable = await solver.checkServer();

// Solve a cube
const cubeString = 'UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB';
const solution = await solver.solve(cubeString);
console.log('Solution:', solution);
```

### Integration with Our Cube State
```javascript
// Create cube state
const cubeState = new CubeState(3);
cubeState.resetToSolved();

// Apply some moves
cubeState.executeMove('R', true);
cubeState.executeMove('U', true);

// Solve using Kociemba
const solver = new KociembaSolver();
const result = await solver.solveOurCube(cubeState);

if (result.success) {
    console.log('Solution:', result.solution);
    console.log('Moves:', result.moves);
} else {
    console.error('Solving failed:', result.error);
}
```

### Enhanced Solver Usage
```javascript
// The enhanced solver automatically chooses the best algorithm
const solver = new Solver();
const result = await solver.solve(cubeState);

console.log('Algorithm used:', result.algorithm);
console.log('Solution length:', result.length);
console.log('Solution:', result.solution);
```

## Testing

### Test Files
1. **`test-kociemba-integration.html`** - Comprehensive integration testing
2. **`run-tests.html`** - General cube functionality tests
3. **`test-cube-fixes.html`** - Basic cube operation tests

### Running Tests
1. Start the Python server: `python kociemba-server.py`
2. Open `test-kociemba-integration.html` in a browser
3. Run all test suites to verify functionality

## Error Handling

### Server Unavailable
- Graceful fallback to simplified algorithms
- Clear error messages to user
- Instructions for server setup

### Invalid Cube States
- Validation before sending to server
- Detailed error messages
- State restoration on failure

### Network Issues
- Timeout handling (default 3 seconds)
- Retry mechanisms
- Offline mode support

## Performance

### Typical Performance
- **Solved Cube**: Instant recognition
- **Simple Scrambles**: 0.1-0.5 seconds
- **Complex Scrambles**: 0.5-3.0 seconds
- **Worst Case**: Up to 5 seconds (with timeout)

### Solution Quality
- **Average Length**: 15-18 moves
- **Maximum Length**: 20 moves (guaranteed)
- **Optimality**: Near-optimal solutions

## Troubleshooting

### Common Issues

#### Server Won't Start
- Check Python installation
- Verify RubiksCube-TwophaseSolver-master directory exists
- Ensure port 8080 is available

#### Slow First Run
- Normal behavior - generating lookup tables
- Wait for "Server is ready" message
- Subsequent runs will be fast

#### CORS Errors
- Server includes CORS headers
- Use `http://localhost` not `file://`
- Check browser console for details

#### Invalid Solutions
- Verify cube state is valid
- Check cube string format
- Ensure proper color mapping

### Debug Mode
Enable debug logging:
```javascript
const solver = new KociembaSolver();
solver.debug = true; // Enable detailed logging
```

## Future Enhancements

### Planned Features
1. **Offline Solving**: JavaScript implementation of simplified Kociemba
2. **Solution Animation**: Step-by-step solution visualization
3. **Multiple Algorithms**: Support for other solving methods
4. **Performance Optimization**: Caching and batch solving

### Advanced Features
1. **Custom Algorithms**: User-defined solving sequences
2. **Solution Analysis**: Move efficiency analysis
3. **Learning Mode**: Educational step-by-step explanations
4. **Competition Mode**: Speed-solving optimizations

## Contributing

### Adding New Algorithms
1. Implement solver interface in `js/solver.js`
2. Add algorithm-specific classes
3. Update solver selection logic
4. Add comprehensive tests

### Improving Performance
1. Optimize cube state conversion
2. Implement client-side caching
3. Add request batching
4. Optimize server communication

## License and Credits

This integration builds upon:
- **RubiksCube-TwophaseSolver-master**: Original Kociemba implementation
- **Herbert Kociemba**: Original algorithm inventor
- **Clean Cube Project**: Our cube visualization and interaction system

The integration maintains compatibility with the original solver while providing a modern JavaScript interface.
