/**
 * Comprehensive Cube Rotation Test Suite
 * Tests all face rotations and edge movements
 */

class CubeRotationTest {
    constructor() {
        this.cube = new CubeState(3);
        this.testResults = [];
    }

    runAllTests() {
        console.log('🧪 Starting Comprehensive Cube Rotation Tests...\n');
        
        this.testBasicRotations();
        this.testEdgeCycles();
        this.testComplexSequences();
        this.testMultipleMoveHandling();
        this.testValidation();
        
        this.printResults();
        return this.testResults;
    }

    testBasicRotations() {
        console.log('📋 Testing Basic Face Rotations...');
        
        const faces = ['U', 'D', 'F', 'B', 'R', 'L'];
        
        for (const face of faces) {
            this.cube.resetToSolved();
            const initialState = this.cube.cubeString;
            
            try {
                // Test clockwise rotation
                this.cube.executeMove(face, true);
                const afterClockwise = this.cube.cubeString;
                
                // Test counter-clockwise rotation
                this.cube.executeMove(face, false);
                const afterCounterClockwise = this.cube.cubeString;
                
                if (afterCounterClockwise === initialState) {
                    this.addResult(`✅ ${face} rotation works correctly`, true);
                } else {
                    this.addResult(`❌ ${face} rotation failed - not reversible`, false);
                    console.log(`Initial: ${initialState}`);
                    console.log(`After ${face}: ${afterClockwise}`);
                    console.log(`After ${face}': ${afterCounterClockwise}`);
                }
                
                // Test that face itself rotates
                this.cube.resetToSolved();
                this.cube.executeMove(face, true);
                
                const faces_after = this.cube.stringToFaces();
                const face_array = faces_after[face];
                
                // Check if face rotated (corners should have moved)
                this.cube.resetToSolved();
                const faces_before = this.cube.stringToFaces();
                const face_before = faces_before[face];
                
                const cornersChanged = (
                    face_array[0][0] !== face_before[0][0] ||
                    face_array[0][2] !== face_before[0][2] ||
                    face_array[2][0] !== face_before[2][0] ||
                    face_array[2][2] !== face_before[2][2]
                );
                
                if (cornersChanged) {
                    this.addResult(`✅ ${face} face itself rotates correctly`, true);
                } else {
                    this.addResult(`❌ ${face} face doesn't rotate itself`, false);
                }
                
            } catch (error) {
                this.addResult(`❌ ${face} rotation threw error: ${error.message}`, false);
            }
        }
    }

    testEdgeCycles() {
        console.log('\n📋 Testing Edge Cycles...');
        
        const testCases = [
            { face: 'U', description: 'U face edge cycle' },
            { face: 'D', description: 'D face edge cycle' },
            { face: 'F', description: 'F face edge cycle' },
            { face: 'B', description: 'B face edge cycle' },
            { face: 'R', description: 'R face edge cycle' },
            { face: 'L', description: 'L face edge cycle' }
        ];
        
        for (const testCase of testCases) {
            this.cube.resetToSolved();
            const initialState = this.cube.cubeString;
            
            try {
                // Execute move 4 times - should return to original state
                for (let i = 0; i < 4; i++) {
                    this.cube.executeMove(testCase.face, true);
                }
                
                if (this.cube.cubeString === initialState) {
                    this.addResult(`✅ ${testCase.description} - 4 moves return to start`, true);
                } else {
                    this.addResult(`❌ ${testCase.description} - 4 moves don't return to start`, false);
                }
                
            } catch (error) {
                this.addResult(`❌ ${testCase.description} threw error: ${error.message}`, false);
            }
        }
    }

    testComplexSequences() {
        console.log('\n📋 Testing Complex Move Sequences...');
        
        const sequences = [
            {
                name: 'R U R\' U\'',
                moves: ['R', 'U', 'R\'', 'U\''],
                cycles: 6,
                description: 'Basic algorithm'
            },
            {
                name: 'F R U\' R\' F\'',
                moves: ['F', 'R', 'U\'', 'R\'', 'F\''],
                cycles: 1,
                description: 'F2L algorithm'
            }
        ];
        
        for (const seq of sequences) {
            this.cube.resetToSolved();
            const initialState = this.cube.cubeString;
            
            try {
                // Execute sequence multiple times
                for (let cycle = 0; cycle < seq.cycles; cycle++) {
                    for (const move of seq.moves) {
                        const face = move.charAt(0);
                        const clockwise = !move.includes('\'');
                        this.cube.executeMove(face, clockwise);
                    }
                }
                
                if (this.cube.cubeString === initialState) {
                    this.addResult(`✅ ${seq.description} (${seq.name}) works correctly`, true);
                } else {
                    this.addResult(`❌ ${seq.description} (${seq.name}) failed`, false);
                }
                
            } catch (error) {
                this.addResult(`❌ ${seq.description} threw error: ${error.message}`, false);
            }
        }
    }

    testMultipleMoveHandling() {
        console.log('\n📋 Testing Multiple Move Handling...');
        
        this.cube.resetToSolved();
        
        try {
            // Test rapid sequence of moves
            const rapidMoves = ['R', 'U', 'R\'', 'F', 'R', 'F\'', 'U\'', 'R\''];
            
            for (const move of rapidMoves) {
                const face = move.charAt(0);
                const clockwise = !move.includes('\'');
                this.cube.executeMove(face, clockwise);
                
                // Validate after each move
                if (!this.cube.validateCubeState()) {
                    throw new Error(`Cube state corrupted after move ${move}`);
                }
            }
            
            this.addResult('✅ Multiple rapid moves handled correctly', true);
            
        } catch (error) {
            this.addResult(`❌ Multiple move handling failed: ${error.message}`, false);
        }
    }

    testValidation() {
        console.log('\n📋 Testing Cube Validation...');
        
        try {
            // Test solved cube validation
            this.cube.resetToSolved();
            if (this.cube.validateCubeState()) {
                this.addResult('✅ Solved cube passes validation', true);
            } else {
                this.addResult('❌ Solved cube fails validation', false);
            }
            
            // Test scrambled cube validation
            const scrambleMoves = ['R', 'U', 'F', 'L', 'D', 'B'];
            for (let i = 0; i < 10; i++) {
                const randomFace = scrambleMoves[Math.floor(Math.random() * scrambleMoves.length)];
                const randomDirection = Math.random() > 0.5;
                this.cube.executeMove(randomFace, randomDirection);
            }
            
            if (this.cube.validateCubeState()) {
                this.addResult('✅ Scrambled cube passes validation', true);
            } else {
                this.addResult('❌ Scrambled cube fails validation', false);
            }
            
            // Test center preservation
            const faces = this.cube.stringToFaces();
            const centersCorrect = (
                faces.U[1][1] === 'white' &&
                faces.D[1][1] === 'yellow' &&
                faces.F[1][1] === 'green' &&
                faces.B[1][1] === 'blue' &&
                faces.R[1][1] === 'red' &&
                faces.L[1][1] === 'orange'
            );
            
            if (centersCorrect) {
                this.addResult('✅ Centers preserved after scrambling', true);
            } else {
                this.addResult('❌ Centers not preserved after scrambling', false);
            }
            
        } catch (error) {
            this.addResult(`❌ Validation test failed: ${error.message}`, false);
        }
    }

    addResult(message, success) {
        this.testResults.push({ message, success });
        console.log(message);
    }

    printResults() {
        console.log('\n📊 Test Results Summary:');
        console.log('========================');
        
        const passed = this.testResults.filter(r => r.success).length;
        const total = this.testResults.length;
        
        console.log(`Passed: ${passed}/${total} tests`);
        console.log(`Success Rate: ${((passed/total) * 100).toFixed(1)}%`);
        
        if (passed === total) {
            console.log('🎉 All tests passed! Cube rotations are working correctly.');
        } else {
            console.log('⚠️  Some tests failed. Check the issues above.');
            
            console.log('\nFailed Tests:');
            this.testResults.filter(r => !r.success).forEach(result => {
                console.log(`  - ${result.message}`);
            });
        }
    }
}

// Export for use in browser or Node.js
if (typeof window !== 'undefined') {
    window.CubeRotationTest = CubeRotationTest;
} else {
    module.exports = CubeRotationTest;
}
