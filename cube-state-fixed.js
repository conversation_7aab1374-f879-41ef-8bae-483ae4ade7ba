/**
 * Fixed Cube State Management - Based on correct reference implementation
 */

class CubeStateFixed {
    constructor(size = 3) {
        this.size = size;
        this.initializeState();
    }

    initializeState() {
        this.colors = {
            'U': 'white',   'D': 'yellow',  'F': 'green',
            'B': 'blue',    'R': 'red',     'L': 'orange'
        };

        this.colorToChar = {
            'white': 'W',   'yellow': 'Y',  'green': 'G',
            'blue': 'B',    'red': 'R',     'orange': 'O'
        };

        this.charToColor = {
            'W': 'white',   'Y': 'yellow',  'G': 'green',
            'B': 'blue',    'R': 'red',     'O': 'orange'
        };

        this.resetToSolved();
    }

    resetToSolved() {
        const stickersPerFace = this.size * this.size;
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        
        this.cubeString = '';
        faceOrder.forEach(face => {
            const color = this.colors[face];
            const char = this.colorToChar[color];
            this.cubeString += char.repeat(stickersPerFace);
        });
    }

    // Convert string position to face and local position
    positionToFacePos(pos) {
        const stickersPerFace = this.size * this.size;
        const faceIndex = Math.floor(pos / stickersPerFace);
        const localPos = pos % stickersPerFace;
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        return { face: faceOrder[faceIndex], localPos };
    }

    // Convert face and local position to string position
    facePosToPosition(face, localPos) {
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        const faceIndex = faceOrder.indexOf(face);
        const stickersPerFace = this.size * this.size;
        return faceIndex * stickersPerFace + localPos;
    }

    // Get sticker at position
    getSticker(pos) {
        return this.cubeString[pos];
    }

    // Set sticker at position
    setSticker(pos, value) {
        const arr = this.cubeString.split('');
        arr[pos] = value;
        this.cubeString = arr.join('');
    }

    // Rotate face itself (positions 0-8 for 3x3)
    rotateFace(face, clockwise = true) {
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        const faceIndex = faceOrder.indexOf(face);
        const stickersPerFace = this.size * this.size;
        const startPos = faceIndex * stickersPerFace;

        // Get current face stickers
        const faceStickers = [];
        for (let i = 0; i < stickersPerFace; i++) {
            faceStickers[i] = this.cubeString[startPos + i];
        }

        // Rotate the face
        const rotated = [];
        for (let row = 0; row < this.size; row++) {
            for (let col = 0; col < this.size; col++) {
                const oldPos = row * this.size + col;
                let newRow, newCol;
                
                if (clockwise) {
                    newRow = col;
                    newCol = this.size - 1 - row;
                } else {
                    newRow = this.size - 1 - col;
                    newCol = row;
                }
                
                const newPos = newRow * this.size + newCol;
                rotated[newPos] = faceStickers[oldPos];
            }
        }

        // Preserve center for 3x3
        if (this.size === 3) {
            rotated[4] = faceStickers[4]; // Center stays the same
        }

        // Set rotated face back
        for (let i = 0; i < stickersPerFace; i++) {
            this.setSticker(startPos + i, rotated[i]);
        }
    }

    // Execute move using position-based approach from reference
    executeMove(face, clockwise = true) {
        console.log(`Executing move: ${face}${clockwise ? '' : "'"}`);

        // Rotate the face itself
        this.rotateFace(face, clockwise);

        // Define adjacent cycles based on reference implementation
        const adjacentCycles = {
            'F': [
                { face: 'U', positions: [6, 7, 8] },      // Bottom row of U
                { face: 'R', positions: [0, 3, 6] },      // Left column of R  
                { face: 'D', positions: [2, 1, 0] },      // Top row of D (reversed)
                { face: 'L', positions: [8, 5, 2] }       // Right column of L (reversed)
            ],
            'B': [
                { face: 'U', positions: [2, 1, 0] },      // Top row of U (reversed)
                { face: 'L', positions: [0, 3, 6] },      // Left column of L
                { face: 'D', positions: [6, 7, 8] },      // Bottom row of D
                { face: 'R', positions: [8, 5, 2] }       // Right column of R (reversed)
            ],
            'R': [
                { face: 'U', positions: [2, 5, 8] },      // Right column of U
                { face: 'B', positions: [6, 3, 0] },      // Left column of B (reversed)
                { face: 'D', positions: [2, 5, 8] },      // Right column of D
                { face: 'F', positions: [2, 5, 8] }       // Right column of F
            ],
            'L': [
                { face: 'U', positions: [0, 3, 6] },      // Left column of U
                { face: 'F', positions: [0, 3, 6] },      // Left column of F
                { face: 'D', positions: [0, 3, 6] },      // Left column of D
                { face: 'B', positions: [8, 5, 2] }       // Right column of B (reversed)
            ],
            'U': [
                { face: 'F', positions: [0, 1, 2] },      // Top row of F
                { face: 'L', positions: [0, 1, 2] },      // Top row of L
                { face: 'B', positions: [0, 1, 2] },      // Top row of B
                { face: 'R', positions: [0, 1, 2] }       // Top row of R
            ],
            'D': [
                { face: 'F', positions: [6, 7, 8] },      // Bottom row of F
                { face: 'R', positions: [6, 7, 8] },      // Bottom row of R
                { face: 'B', positions: [6, 7, 8] },      // Bottom row of B
                { face: 'L', positions: [6, 7, 8] }       // Bottom row of L
            ]
        };

        const cycle = adjacentCycles[face];
        if (!cycle) return;

        // Get current values from all positions in the cycle
        const values = cycle.map(item => 
            item.positions.map(pos => {
                const globalPos = this.facePosToPosition(item.face, pos);
                return this.getSticker(globalPos);
            })
        );

        // Rotate values in the cycle
        if (clockwise) {
            // Clockwise: move values forward in cycle
            const temp = values[0];
            for (let i = 0; i < values.length - 1; i++) {
                values[i] = values[i + 1];
            }
            values[values.length - 1] = temp;
        } else {
            // Counterclockwise: move values backward in cycle
            const temp = values[values.length - 1];
            for (let i = values.length - 1; i > 0; i--) {
                values[i] = values[i - 1];
            }
            values[0] = temp;
        }

        // Set rotated values back
        cycle.forEach((item, i) => {
            item.positions.forEach((pos, j) => {
                const globalPos = this.facePosToPosition(item.face, pos);
                this.setSticker(globalPos, values[i][j]);
            });
        });

        return this.cubeString;
    }

    isSolved() {
        const stickersPerFace = this.size * this.size;
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        
        for (let faceIndex = 0; faceIndex < 6; faceIndex++) {
            const startPos = faceIndex * stickersPerFace;
            const firstSticker = this.cubeString[startPos];
            
            for (let i = 1; i < stickersPerFace; i++) {
                if (this.cubeString[startPos + i] !== firstSticker) {
                    return false;
                }
            }
        }
        
        return true;
    }
}

// Test the fixed implementation
function testFixed() {
    console.log('=== Testing Fixed Implementation ===');
    
    const cube = new CubeStateFixed(3);
    const initial = cube.cubeString;
    
    console.log('Initial:', initial.substring(0, 20) + '...');
    
    // Test simple R R' sequence first
    cube.executeMove('R', true);
    console.log('After R:', cube.cubeString.substring(0, 20) + '...');

    cube.executeMove('R', false);
    console.log('After R\':', cube.cubeString.substring(0, 20) + '...');

    if (cube.cubeString === initial) {
        console.log('✓ R R\' works correctly');
    } else {
        console.log('✗ R R\' failed');
        return false;
    }

    // Test R2 R2 sequence (should return to solved)
    cube.executeMove('R', true);
    cube.executeMove('R', true);
    console.log('After R2:', cube.cubeString.substring(0, 20) + '...');

    cube.executeMove('R', true);
    cube.executeMove('R', true);
    console.log('After R2 again:', cube.cubeString.substring(0, 20) + '...');

    const final = cube.cubeString;
    console.log('Final:', final.substring(0, 20) + '...');

    if (final === initial) {
        console.log('✓ SUCCESS: R2 R2 returned to solved state!');
        return true;
    } else {
        console.log('✗ FAILED: R2 R2 did not return to solved state');

        // Test if R U R' U' is actually supposed to return to solved
        // (It's not - it's a corner algorithm)
        console.log('\nNote: R U R\' U\' is a corner algorithm and should NOT return to solved state.');
        console.log('Testing R4 instead (should return to solved):');

        const cube2 = new CubeStateFixed(3);
        cube2.executeMove('R', true);
        cube2.executeMove('R', true);
        cube2.executeMove('R', true);
        cube2.executeMove('R', true);

        if (cube2.cubeString === initial) {
            console.log('✓ SUCCESS: R4 returned to solved state!');
            return true;
        } else {
            console.log('✗ FAILED: R4 did not return to solved state');
            return false;
        }
    }
}

// Export for use
if (typeof window !== 'undefined') {
    window.CubeStateFixed = CubeStateFixed;
    window.testFixed = testFixed;
} else {
    module.exports = { CubeStateFixed, testFixed };
}

// Run test if in Node.js
if (typeof window === 'undefined') {
    testFixed();
}
