/**
 * CORRECTED Cube State Implementation
 * Fixes the D move edge cycle bug and improves validation
 */

class CubeState {
    constructor(size = 3) {
        this.size = size;
        // Fixed color mappings - centers must never change
        this.colors = {
            'U': 'white',   // Up center is always white
            'D': 'yellow',  // Down center is always yellow
            'F': 'green',   // Front center is always green
            'B': 'blue',    // Back center is always blue
            'R': 'red',     // Right center is always red
            'L': 'orange'   // Left center is always orange
        };

        this.colorToChar = {
            'white': 'W', 'yellow': 'Y', 'green': 'G',
            'blue': 'B', 'red': 'R', 'orange': 'O'
        };

        this.charToColor = {
            'W': 'white', 'Y': 'yellow', 'G': 'green',
            'B': 'blue', 'R': 'red', 'O': 'orange'
        };

        this.resetToSolved();
    }

    resetToSolved() {
        const stickersPerFace = this.size * this.size;
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        this.cubeString = '';

        faceOrder.forEach(face => {
            const color = this.colors[face];
            const char = this.colorToChar[color];
            this.cubeString += char.repeat(stickersPerFace);
        });

        console.log('Reset to solved state:', this.cubeString);
    }

    stringToFaces() {
        const faces = {};
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        const stickersPerFace = this.size * this.size;

        faceOrder.forEach((face, faceIndex) => {
            faces[face] = [];
            for (let row = 0; row < this.size; row++) {
                faces[face][row] = [];
                for (let col = 0; col < this.size; col++) {
                    const stringIndex = faceIndex * stickersPerFace + row * this.size + col;
                    const char = this.cubeString[stringIndex];
                    const color = this.charToColor[char] || 'gray';
                    faces[face][row][col] = color;
                }
            }
        });

        return faces;
    }

    facesToString(faces) {
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        let result = '';

        faceOrder.forEach(face => {
            for (let row = 0; row < this.size; row++) {
                for (let col = 0; col < this.size; col++) {
                    const color = faces[face][row][col];
                    const char = this.colorToChar[color] || 'W';
                    result += char;
                }
            }
        });

        return result;
    }

    validateCubeState() {
        try {
            // Check string length
            const expectedLength = this.size * this.size * 6;
            if (this.cubeString.length !== expectedLength) {
                console.error(`Invalid cube string length: ${this.cubeString.length}, expected ${expectedLength}`);
                return false;
            }

            // Count each color
            const counts = {};
            for (let char of this.cubeString) {
                counts[char] = (counts[char] || 0) + 1;
            }

            // Each color must appear exactly size*size times
            const expectedCount = this.size * this.size;
            const colors = ['W', 'Y', 'G', 'B', 'R', 'O'];

            for (const color of colors) {
                if (counts[color] !== expectedCount) {
                    console.error(`VALIDATION FAILED: Color ${color} appears ${counts[color]} times, expected ${expectedCount}`);
                    console.error('All counts:', counts);
                    console.error('Cube string:', this.cubeString);
                    return false;
                }
            }

            // Verify centers are preserved (for 3x3)
            if (this.size === 3) {
                const faces = this.stringToFaces();
                const expectedCenters = {
                    'U': 'white', 'D': 'yellow', 'F': 'green',
                    'B': 'blue', 'R': 'red', 'L': 'orange'
                };

                for (const [face, expectedColor] of Object.entries(expectedCenters)) {
                    const actualColor = faces[face][1][1]; // Center position
                    if (actualColor !== expectedColor) {
                        console.error(`CENTER CORRUPTED: ${face} center is ${actualColor}, should be ${expectedColor}`);
                        return false;
                    }
                }
            }

            return true;
        } catch (error) {
            console.error('Validation error:', error);
            return false;
        }
    }

    executeMove(face, clockwise = true) {
        console.log(`Executing move: ${face}${clockwise ? '' : "'"}`);
        const beforeString = this.cubeString;

        try {
            // Validate input
            if (!['U', 'D', 'F', 'B', 'R', 'L'].includes(face)) {
                throw new Error(`Invalid face: ${face}`);
            }

            const cube = this.cubeString.split('');

            // Rotate the face itself
            this.rotateFace(cube, face, clockwise);

            // Rotate adjacent edges
            this.rotateEdges(cube, face, clockwise);

            this.cubeString = cube.join('');

            // Validate the move didn't corrupt the cube
            if (!this.validateCubeState()) {
                throw new Error(`Move ${face}${clockwise ? '' : "'"} corrupted cube state`);
            }

            console.log(`Move ${face}${clockwise ? '' : "'"} completed successfully`);
            return this.cubeString;

        } catch (error) {
            console.error(`MOVE FAILED: ${error.message}`);
            // Restore previous state
            this.cubeString = beforeString;
            throw error;
        }
    }

    rotateFace(cube, face, clockwise) {
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        const faceIndex = faceOrder.indexOf(face);
        const start = faceIndex * 9;

        // Save original face
        const original = [];
        for (let i = 0; i < 9; i++) {
            original[i] = cube[start + i];
        }

        if (clockwise) {
            // Clockwise rotation: 90 degrees CW
            cube[start + 0] = original[6]; cube[start + 1] = original[3]; cube[start + 2] = original[0];
            cube[start + 3] = original[7]; cube[start + 4] = original[4]; cube[start + 5] = original[1];
            cube[start + 6] = original[8]; cube[start + 7] = original[5]; cube[start + 8] = original[2];
        } else {
            // Counter-clockwise rotation: 90 degrees CCW
            cube[start + 0] = original[2]; cube[start + 1] = original[5]; cube[start + 2] = original[8];
            cube[start + 3] = original[1]; cube[start + 4] = original[4]; cube[start + 5] = original[7];
            cube[start + 6] = original[0]; cube[start + 7] = original[3]; cube[start + 8] = original[6];
        }
    }

    rotateEdges(cube, face, clockwise) {
        // CORRECTED edge cycles with proper position mapping
        // Face positions: U(0-8), R(9-17), F(18-26), D(27-35), L(36-44), B(45-53)
        // Each face is stored as: [0,1,2,3,4,5,6,7,8] representing:
        // [top-left, top-center, top-right, middle-left, center, middle-right, bottom-left, bottom-center, bottom-right]

        const edgeMappings = {
            'U': {
                // U face rotates: F-top -> R-top -> B-top -> L-top
                positions: [
                    [18, 19, 20], // F top row
                    [9, 10, 11],  // R top row
                    [45, 46, 47], // B top row
                    [36, 37, 38]  // L top row
                ]
            },
            'D': {
                // D face rotates: F-bottom -> L-bottom -> B-bottom -> R-bottom
                positions: [
                    [24, 25, 26], // F bottom row
                    [42, 43, 44], // L bottom row
                    [51, 52, 53], // B bottom row
                    [15, 16, 17]  // R bottom row
                ]
            },
            'F': {
                // F face rotates: U-bottom -> R-left -> D-top -> L-right (with reversals)
                positions: [
                    [6, 7, 8],    // U bottom row
                    [9, 12, 15],  // R left column
                    [27, 28, 29], // D top row
                    [44, 41, 38]  // L right column (reversed order)
                ]
            },
            'B': {
                // B face rotates: U-top -> L-left -> D-bottom -> R-right (with reversals)
                positions: [
                    [2, 1, 0],    // U top row (reversed)
                    [36, 39, 42], // L left column
                    [33, 34, 35], // D bottom row
                    [17, 14, 11]  // R right column (reversed)
                ]
            },
            'R': {
                // R face rotates: U-right -> F-right -> D-right -> B-left (with reversals)
                positions: [
                    [2, 5, 8],    // U right column
                    [20, 23, 26], // F right column
                    [29, 32, 35], // D right column
                    [53, 50, 47]  // B left column (reversed)
                ]
            },
            'L': {
                // L face rotates: U-left -> B-right -> D-left -> F-left (with reversals)
                positions: [
                    [0, 3, 6],    // U left column
                    [45, 48, 51], // B right column (reversed)
                    [27, 30, 33], // D left column
                    [18, 21, 24]  // F left column
                ]
            }
        };

        const mapping = edgeMappings[face];
        if (!mapping) {
            console.error(`Invalid face for edge rotation: ${face}`);
            return;
        }

        const positions = mapping.positions;
        const temp = [cube[positions[0][0]], cube[positions[0][1]], cube[positions[0][2]]];

        if (clockwise) {
            // Rotate clockwise: 0->1->2->3->0
            for (let i = 0; i < 3; i++) {
                cube[positions[0][i]] = cube[positions[3][i]];
                cube[positions[3][i]] = cube[positions[2][i]];
                cube[positions[2][i]] = cube[positions[1][i]];
                cube[positions[1][i]] = temp[i];
            }
        } else {
            // Rotate counter-clockwise: 0->3->2->1->0
            for (let i = 0; i < 3; i++) {
                cube[positions[0][i]] = cube[positions[1][i]];
                cube[positions[1][i]] = cube[positions[2][i]];
                cube[positions[2][i]] = cube[positions[3][i]];
                cube[positions[3][i]] = temp[i];
            }
        }
    }

    generateScramble(moveCount = 20) {
        const faces = ['U', 'D', 'F', 'B', 'R', 'L'];
        const modifiers = ['', "'"]; // Only single rotations, no double moves
        const scramble = [];
        let lastFace = '';

        for (let i = 0; i < moveCount; i++) {
            let face;
            do {
                face = faces[Math.floor(Math.random() * faces.length)];
            } while (face === lastFace && faces.length > 1);

            const modifier = modifiers[Math.floor(Math.random() * modifiers.length)];
            scramble.push(face + modifier);
            lastFace = face;
        }

        console.log('Generated single-move scramble:', scramble.join(' '));
        return scramble.join(' ');
    }

    applyScramble(scrambleString) {
        console.log('Applying scramble:', scrambleString);

        try {
            const moves = scrambleString.split(' ').filter(move => move.trim());

            for (const move of moves) {
                const trimmedMove = move.trim();
                if (!trimmedMove) continue;

                const face = trimmedMove[0].toUpperCase();
                const clockwise = !trimmedMove.includes("'");

                if (!['U', 'D', 'F', 'B', 'R', 'L'].includes(face)) {
                    console.warn(`Invalid move in scramble: ${trimmedMove}`);
                    continue;
                }

                // Only single moves - no double moves
                this.executeMove(face, clockwise);
            }

            console.log('Scramble applied successfully');
            return this.cubeString;

        } catch (error) {
            console.error('Scramble failed:', error);
            throw error;
        }
    }

    applySolution(solutionString) {
        console.log('Applying solution:', solutionString);
        return this.applyScramble(solutionString);
    }

    isSolved() {
        const faces = this.stringToFaces();

        for (const face of ['U', 'R', 'F', 'D', 'L', 'B']) {
            const faceArray = faces[face];
            const firstColor = faceArray[0][0];

            for (let row = 0; row < this.size; row++) {
                for (let col = 0; col < this.size; col++) {
                    if (faceArray[row][col] !== firstColor) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    getKociembaString() {
        if (this.size !== 3) {
            console.error('Kociemba format only supported for 3x3 cubes');
            return null;
        }

        try {
            // Validate cube state before conversion
            if (!this.validateCubeState()) {
                console.error('Cannot generate Kociemba string: cube state is invalid');
                return null;
            }

            // Additional validation: check if cube is in a solvable state
            if (!this.isSolvableState()) {
                console.error('Cube is in an unsolvable state');
                return null;
            }

            // Convert from WYGBRO to URFDLB format for Kociemba
            const mapping = {
                'W': 'U', // White -> Up
                'Y': 'D', // Yellow -> Down
                'G': 'F', // Green -> Front
                'B': 'B', // Blue -> Back
                'R': 'R', // Red -> Right
                'O': 'L'  // Orange -> Left
            };

            const kociembaString = this.cubeString.split('').map(c => mapping[c] || c).join('');
            console.log('Generated Kociemba string:', kociembaString);
            return kociembaString;

        } catch (error) {
            console.error('Error generating Kociemba string:', error);
            return null;
        }
    }

    // Check if the cube is in a theoretically solvable state
    isSolvableState() {
        try {
            // For cubes generated by valid moves, they should always be solvable
            // This is a basic check - more complex parity checks could be added

            // Check that we have the right number of each color
            if (!this.validateCubeState()) {
                return false;
            }

            // Check that centers are in correct positions (for 3x3)
            if (this.size === 3) {
                const faces = this.stringToFaces();
                const expectedCenters = {
                    'U': 'white', 'D': 'yellow', 'F': 'green',
                    'B': 'blue', 'R': 'red', 'L': 'orange'
                };

                for (const [face, expectedColor] of Object.entries(expectedCenters)) {
                    const actualColor = faces[face][1][1]; // Center position
                    if (actualColor !== expectedColor) {
                        console.error(`Center check failed: ${face} center is ${actualColor}, should be ${expectedColor}`);
                        return false;
                    }
                }
            }

            return true;

        } catch (error) {
            console.error('Error checking solvable state:', error);
            return false;
        }
    }

    changeSize(newSize) {
        if (![2, 3, 4].includes(newSize)) {
            console.error(`Invalid cube size: ${newSize}`);
            return;
        }

        this.size = newSize;
        this.resetToSolved();
        return this.cubeString;
    }

    executeWideMove(wideFace, clockwise = true) {
        if (this.size !== 4) {
            console.warn('Wide moves only supported for 4x4 cubes');
            return this.cubeString;
        }

        const baseFace = wideFace.replace('w', '');
        this.executeMove(baseFace, clockwise);
        return this.cubeString;
    }
}

// Export for use in other modules
window.CubeState = CubeState;