/**
 * Clean 3D Cube Visualization
 * Handles Three.js 3D rendering independent of cube state
 */

class Cube3D {
    constructor(canvas, cubeState) {
        this.canvas = canvas;
        this.cubeState = cubeState;
        this.visualOrientation = { x: 0, y: 0, z: 0 };

        // Mouse control properties
        this.isMouseDown = false;
        this.mouseX = 0;
        this.mouseY = 0;
        this.autoRotate = true;
        this.autoRotateSpeed = 0.005;

        this.init3D();
        this.setupMouseControls();
    }

    init3D() {
        // Scene setup
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1a1a1a);

        // Camera setup
        this.camera = new THREE.PerspectiveCamera(
            75, 
            this.canvas.width / this.canvas.height, 
            0.1, 
            1000
        );
        this.camera.position.set(4, 4, 4);
        this.camera.lookAt(0, 0, 0);

        // Renderer setup
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: this.canvas,
            antialias: true 
        });
        this.renderer.setSize(this.canvas.width, this.canvas.height);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // Lighting
        this.setupLighting();

        // Create cube group
        this.cubeGroup = new THREE.Group();
        this.scene.add(this.cubeGroup);

        // Initialize cube
        this.createCube();

        // Start render loop
        this.animate();
    }

    setupMouseControls() {
        // Mouse event listeners for cube rotation
        this.canvas.addEventListener('mousedown', (event) => {
            this.isMouseDown = true;
            this.mouseX = event.clientX;
            this.mouseY = event.clientY;
            this.autoRotate = false; // Stop auto rotation when user interacts
            this.canvas.style.cursor = 'grabbing';
        });

        this.canvas.addEventListener('mousemove', (event) => {
            if (!this.isMouseDown) return;

            const deltaX = event.clientX - this.mouseX;
            const deltaY = event.clientY - this.mouseY;

            // Rotate cube based on mouse movement
            this.cubeGroup.rotation.y += deltaX * 0.01;
            this.cubeGroup.rotation.x += deltaY * 0.01;

            // Update visual orientation tracking
            this.visualOrientation.y += deltaX * 0.01;
            this.visualOrientation.x += deltaY * 0.01;

            this.mouseX = event.clientX;
            this.mouseY = event.clientY;
        });

        this.canvas.addEventListener('mouseup', () => {
            this.isMouseDown = false;
            this.canvas.style.cursor = 'grab';

            // Resume auto rotation after 2 seconds of no interaction
            setTimeout(() => {
                if (!this.isMouseDown) {
                    this.autoRotate = true;
                }
            }, 2000);
        });

        this.canvas.addEventListener('mouseleave', () => {
            this.isMouseDown = false;
            this.canvas.style.cursor = 'grab';
        });

        // Set initial cursor
        this.canvas.style.cursor = 'grab';

        // Add double-click to toggle auto-rotation
        this.canvas.addEventListener('dblclick', () => {
            this.autoRotate = !this.autoRotate;
            console.log('Auto-rotation:', this.autoRotate ? 'enabled' : 'disabled');
        });
    }

    // Public method to toggle auto-rotation
    toggleAutoRotation() {
        this.autoRotate = !this.autoRotate;
        console.log('Auto-rotation:', this.autoRotate ? 'enabled' : 'disabled');
        return this.autoRotate;
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // Point light for better illumination
        const pointLight = new THREE.PointLight(0xffffff, 0.5);
        pointLight.position.set(-10, -10, -5);
        this.scene.add(pointLight);
    }

    createCube() {
        // Clear existing cube
        this.cubeGroup.clear();
        this.cubies = [];

        const size = this.cubeState.size;
        const spacing = 1.1;
        const offset = (size - 1) * spacing / 2;

        // Create individual cubies
        for (let x = 0; x < size; x++) {
            for (let y = 0; y < size; y++) {
                for (let z = 0; z < size; z++) {
                    const cubie = this.createCubie(x, y, z, size);
                    cubie.position.set(
                        x * spacing - offset,
                        y * spacing - offset,
                        z * spacing - offset
                    );
                    
                    // Store position info for color updates
                    cubie.userData = { x, y, z, size };
                    
                    this.cubies.push(cubie);
                    this.cubeGroup.add(cubie);
                }
            }
        }

        this.updateColors();
    }

    createCubie(x, y, z, size) {
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        
        // Create materials for each face
        const materials = this.createCubieMaterials(x, y, z, size);
        
        const cubie = new THREE.Mesh(geometry, materials);
        cubie.castShadow = true;
        cubie.receiveShadow = true;
        
        return cubie;
    }

    createCubieMaterials(x, y, z, size) {
        const materials = [];
        const faces = this.cubeState.stringToFaces();
        
        // Face order: right, left, top, bottom, front, back
        const facePositions = [
            { face: 'R', condition: x === size - 1 },  // Right
            { face: 'L', condition: x === 0 },         // Left  
            { face: 'U', condition: y === size - 1 },  // Top
            { face: 'D', condition: y === 0 },         // Bottom
            { face: 'F', condition: z === size - 1 },  // Front
            { face: 'B', condition: z === 0 }          // Back
        ];

        facePositions.forEach(({ face, condition }) => {
            if (condition) {
                // This is a visible face - get color from cube state
                const color = this.getFaceColor(faces, face, x, y, z, size);
                materials.push(this.createColorMaterial(color));
            } else {
                // Internal face - use black
                materials.push(this.createColorMaterial('black'));
            }
        });

        return materials;
    }

    getFaceColor(faces, face, x, y, z, size) {
        let row, col;

        // CORRECTED mapping for 3D cube positions to 2D face arrays
        // The cube state stores faces as 2D arrays where [0][0] is top-left when looking at the face
        switch (face) {
            case 'U': // Top face (y = size-1) - looking down at the cube
                row = size - 1 - z; // z=0 is back, z=size-1 is front
                col = x;             // x=0 is left, x=size-1 is right
                break;
            case 'D': // Bottom face (y = 0) - looking up at the cube
                row = z;             // z=0 is back, z=size-1 is front
                col = x;             // x=0 is left, x=size-1 is right
                break;
            case 'F': // Front face (z = size-1) - looking at the front
                row = size - 1 - y; // y=0 is bottom, y=size-1 is top
                col = x;             // x=0 is left, x=size-1 is right
                break;
            case 'B': // Back face (z = 0) - looking at the back (mirrored)
                row = size - 1 - y; // y=0 is bottom, y=size-1 is top
                col = size - 1 - x; // x=0 is right when looking from behind, x=size-1 is left
                break;
            case 'R': // Right face (x = size-1) - looking at the right side
                row = size - 1 - y; // y=0 is bottom, y=size-1 is top
                col = size - 1 - z; // z=0 is front when looking from right, z=size-1 is back
                break;
            case 'L': // Left face (x = 0) - looking at the left side
                row = size - 1 - y; // y=0 is bottom, y=size-1 is top
                col = z;             // z=0 is back when looking from left, z=size-1 is front
                break;
            default:
                console.warn('Unknown face:', face);
                return 'gray';
        }

        // Validate indices
        if (row < 0 || row >= size || col < 0 || col >= size) {
            console.warn(`Invalid indices for face ${face}: row=${row}, col=${col}, size=${size}, x=${x}, y=${y}, z=${z}`);
            return 'gray';
        }

        if (faces[face] && faces[face][row] && faces[face][row][col] !== undefined) {
            return faces[face][row][col];
        }

        console.warn(`No color found for face ${face} at [${row}][${col}], x=${x}, y=${y}, z=${z}`);
        return 'gray';
    }

    createColorMaterial(color) {
        const colorMap = {
            'white': 0xffffff,
            'yellow': 0xffff00,
            'red': 0xff0000,
            'orange': 0xff8000,
            'green': 0x00ff00,
            'blue': 0x0000ff,
            'black': 0x000000,
            'gray': 0x808080
        };

        const hexColor = colorMap[color] || colorMap['gray'];
        
        return new THREE.MeshLambertMaterial({
            color: hexColor,
            transparent: color === 'black',
            opacity: color === 'black' ? 0.1 : 1.0
        });
    }

    updateColors() {
        if (!this.cubies || this.cubies.length === 0) {
            console.warn('No cubies to update colors for');
            return;
        }

        console.log('Updating 3D colors...');
        const faces = this.cubeState.stringToFaces();
        const size = this.cubeState.size;

        // Update each cubie's materials
        this.cubies.forEach((cubie, index) => {
            const { x, y, z } = cubie.userData;

            // Face order in Three.js materials: right, left, top, bottom, front, back
            const facePositions = [
                { face: 'R', condition: x === size - 1 },  // Right
                { face: 'L', condition: x === 0 },         // Left
                { face: 'U', condition: y === size - 1 },  // Top
                { face: 'D', condition: y === 0 },         // Bottom
                { face: 'F', condition: z === size - 1 },  // Front
                { face: 'B', condition: z === 0 }          // Back
            ];

            // Update materials for each face
            facePositions.forEach(({ face, condition }, materialIndex) => {
                if (condition) {
                    // This is a visible face - get color from cube state
                    const color = this.getFaceColor(faces, face, x, y, z, size);
                    const newMaterial = this.createColorMaterial(color);

                    // Replace the material
                    if (cubie.material[materialIndex]) {
                        cubie.material[materialIndex].dispose(); // Clean up old material
                    }
                    cubie.material[materialIndex] = newMaterial;
                } else {
                    // Internal face - use black/transparent
                    const newMaterial = this.createColorMaterial('black');
                    if (cubie.material[materialIndex]) {
                        cubie.material[materialIndex].dispose();
                    }
                    cubie.material[materialIndex] = newMaterial;
                }
            });
        });

        console.log('3D colors updated successfully');

        this.cubies.forEach(cubie => {
            if (!cubie.userData) return;

            const { x, y, z, size } = cubie.userData;
            const materials = this.createCubieMaterials(x, y, z, size);
            cubie.material = materials;
        });

        console.log('3D colors updated');
    }

    // Visual orientation controls (don't affect cube state)
    rotateLeft() {
        this.cubeGroup.rotation.y += Math.PI / 2;
        this.visualOrientation.y += Math.PI / 2;
        console.log('3D view rotated left (visual only)');
    }

    rotateRight() {
        this.cubeGroup.rotation.y -= Math.PI / 2;
        this.visualOrientation.y -= Math.PI / 2;
        console.log('3D view rotated right (visual only)');
    }

    flip() {
        this.cubeGroup.rotation.x += Math.PI;
        this.visualOrientation.x += Math.PI;
        console.log('3D view flipped (visual only)');
    }

    // Animate a face rotation (visual only, doesn't change cube state)
    animateFaceRotation(face, clockwise, duration = 300) {
        return new Promise((resolve) => {
            const size = this.cubeState.size;
            const rotatingCubies = [];

            // Find cubies that belong to the rotating face
            this.cubies.forEach(cubie => {
                const { x, y, z } = cubie.userData;
                let shouldRotate = false;

                switch (face) {
                    case 'U': shouldRotate = (y === size - 1); break;
                    case 'D': shouldRotate = (y === 0); break;
                    case 'F': shouldRotate = (z === size - 1); break;
                    case 'B': shouldRotate = (z === 0); break;
                    case 'R': shouldRotate = (x === size - 1); break;
                    case 'L': shouldRotate = (x === 0); break;
                }

                if (shouldRotate) {
                    rotatingCubies.push(cubie);
                }
            });

            // Create rotation group
            const rotationGroup = new THREE.Group();
            this.scene.add(rotationGroup);

            // Move rotating cubies to rotation group
            rotatingCubies.forEach(cubie => {
                const worldPosition = new THREE.Vector3();
                cubie.getWorldPosition(worldPosition);
                this.cubeGroup.remove(cubie);
                rotationGroup.add(cubie);
                cubie.position.copy(worldPosition);
            });

            // Animate rotation
            const startTime = Date.now();
            const rotationAxis = this.getRotationAxis(face);
            const rotationAngle = clockwise ? Math.PI / 2 : -Math.PI / 2;

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const easeProgress = this.easeInOutCubic(progress);

                rotationGroup.rotation[rotationAxis] = rotationAngle * easeProgress;

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    // Animation complete - move cubies back to main group
                    rotatingCubies.forEach(cubie => {
                        const worldPosition = new THREE.Vector3();
                        cubie.getWorldPosition(worldPosition);
                        rotationGroup.remove(cubie);
                        this.cubeGroup.add(cubie);
                        cubie.position.copy(worldPosition);
                    });

                    this.scene.remove(rotationGroup);
                    resolve();
                }
            };

            animate();
        });
    }

    getRotationAxis(face) {
        switch (face) {
            case 'U':
            case 'D':
                return 'y';
            case 'F':
            case 'B':
                return 'z';
            case 'R':
            case 'L':
                return 'x';
            default:
                return 'y';
        }
    }

    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    // Update visualization when cube state changes
    refresh() {
        this.updateColors();
    }

    // Resize canvas
    resize(width, height) {
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    }

    // Animation loop
    animate() {
        requestAnimationFrame(() => this.animate());

        // Auto rotation only when not being controlled by mouse
        if (this.autoRotate && !this.isMouseDown) {
            this.cubeGroup.rotation.x += this.autoRotateSpeed;
            this.cubeGroup.rotation.y += this.autoRotateSpeed * 1.5;
        }

        this.renderer.render(this.scene, this.camera);
    }

    // Recreate cube when size changes
    changeSize() {
        this.createCube();
    }
}

// Export for use in other modules
window.Cube3D = Cube3D;
