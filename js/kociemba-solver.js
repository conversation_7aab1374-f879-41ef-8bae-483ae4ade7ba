/**
 * Kociemba Two-Phase Algorithm Solver
 * JavaScript implementation with server communication
 */

class KociembaSolver {
    constructor() {
        this.serverUrl = 'http://localhost:8080';
        this.isServerRunning = false;
        this.maxLength = 20;
        this.timeout = 3;
    }

    async checkServer() {
        /**
         * Check if the Kociemba server is running
         */
        try {
            const response = await fetch(`${this.serverUrl}/UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB`, {
                method: 'GET',
                timeout: 1000
            });
            
            if (response.ok) {
                this.isServerRunning = true;
                console.log('✅ Kociemba server is running');
                return true;
            }
        } catch (error) {
            this.isServerRunning = false;
            console.log('❌ Kociemba server not available:', error.message);
        }
        return false;
    }

    async solve(cubeString, maxLength = 20, timeout = 3) {
        /**
         * Solve a cube using the Kociemba two-phase algorithm
         * @param {string} cubeString - Kociemba format cube string
         * @param {number} maxLength - Maximum solution length
         * @param {number} timeout - Timeout in seconds
         * @returns {Promise<string>} Solution string or error message
         */
        
        // First try server-based solving
        if (await this.checkServer()) {
            return await this.solveWithServer(cubeString, maxLength, timeout);
        }
        
        // Fallback to simplified solver
        console.log('Using fallback solver...');
        return await this.solveWithFallback(cubeString);
    }

    async solveWithServer(cubeString, maxLength = 20, timeout = 3) {
        /**
         * Solve using the Python Kociemba server
         */
        try {
            const url = `${this.serverUrl}/${cubeString}`;
            console.log(`Sending request to: ${url}`);
            
            const response = await fetch(url, {
                method: 'GET',
                timeout: timeout * 1000
            });

            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }

            const solution = await response.text();
            console.log('Server solution:', solution);
            
            // Parse the solution
            return this.parseSolution(solution);
            
        } catch (error) {
            console.error('Server solving failed:', error);
            throw new Error(`Server solving failed: ${error.message}`);
        }
    }

    async solveWithFallback(cubeString) {
        /**
         * Fallback solver using simplified algorithms
         * This is a basic implementation for when the server is not available
         */
        console.log('Using simplified fallback solver...');
        
        // Check if already solved
        const solvedString = 'UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB';
        if (cubeString === solvedString) {
            return 'Already solved!';
        }

        // For now, return a message indicating server is needed
        return 'Complex solving requires Kociemba server. Please start the Python server on port 8080.';
    }

    parseSolution(solutionString) {
        /**
         * Parse the solution string from the server
         * Convert from Kociemba format (U1, R2, F3) to standard format (U, R2, F')
         */
        if (solutionString.includes('Error')) {
            return solutionString;
        }

        // Remove the move count at the end (e.g., "(19f)")
        let solution = solutionString.replace(/\s*\(\d+f?\)\s*$/, '').trim();
        
        // Convert Kociemba notation to standard notation
        solution = solution.replace(/([URFDLB])1/g, '$1');   // U1 -> U
        solution = solution.replace(/([URFDLB])3/g, "$1'");  // U3 -> U'
        // U2 stays as U2
        
        return solution;
    }

    convertFromOurFormat(cubeState) {
        /**
         * Convert from our cube state to Kociemba format
         */
        const faceCube = new FaceCube();
        return faceCube.fromOurCubeState(cubeState);
    }

    async solveOurCube(cubeState, maxLength = 20, timeout = 3) {
        /**
         * Solve a cube in our format
         */
        try {
            // Convert to Kociemba format
            const kociembaString = this.convertFromOurFormat(cubeState);
            if (!kociembaString) {
                throw new Error('Failed to convert cube to Kociemba format');
            }

            console.log('Kociemba string:', kociembaString);

            // Solve using Kociemba algorithm
            const solution = await this.solve(kociembaString, maxLength, timeout);
            
            return {
                success: true,
                solution: solution,
                kociembaString: kociembaString,
                moves: this.parseMoves(solution)
            };

        } catch (error) {
            console.error('Solving failed:', error);
            return {
                success: false,
                error: error.message,
                solution: null
            };
        }
    }

    parseMoves(solutionString) {
        /**
         * Parse solution string into individual moves
         */
        if (!solutionString || solutionString.includes('Error') || solutionString.includes('server')) {
            return [];
        }

        return solutionString.trim().split(/\s+/).filter(move => move.length > 0);
    }

    async startPythonServer() {
        /**
         * Instructions for starting the Python server
         */
        const instructions = `
To use the full Kociemba solver, please start the Python server:

1. Navigate to the RubiksCube-TwophaseSolver-master folder
2. Run: python start_server.py 8080 20 3
3. Wait for "Server now listening..." message
4. The server will be available at http://localhost:8080

Note: First run may take 30+ minutes to generate lookup tables.
        `;
        
        console.log(instructions);
        return instructions;
    }

    getServerStatus() {
        /**
         * Get current server status
         */
        return {
            isRunning: this.isServerRunning,
            url: this.serverUrl,
            maxLength: this.maxLength,
            timeout: this.timeout
        };
    }

    setServerConfig(url, maxLength = 20, timeout = 3) {
        /**
         * Configure server settings
         */
        this.serverUrl = url;
        this.maxLength = maxLength;
        this.timeout = timeout;
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.KociembaSolver = KociembaSolver;
} else {
    module.exports = KociembaSolver;
}
