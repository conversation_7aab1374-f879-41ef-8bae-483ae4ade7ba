/**
 * Clean Controls Handler
 * Manages user interactions and coordinates between components
 */

class Controls {
    constructor(cubeState, cube3D, cube2D) {
        this.cubeState = cubeState;
        this.cube3D = cube3D;
        this.cube2D = cube2D;
        this.animationSpeed = 400; // Default animation speed in milliseconds

        this.setupEventListeners();
    }

    setupEventListeners() {
        // Size selector buttons
        document.querySelectorAll('.size-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const size = parseInt(e.target.dataset.size);
                this.changeSize(size);
            });
        });

        // Orientation controls (visual only)
        document.querySelectorAll('.orientation-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                this.handleOrientation(action);
            });
        });

        // Face rotation buttons
        document.querySelectorAll('.face-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const face = e.target.dataset.face;
                const clockwise = e.target.dataset.clockwise === 'true';
                this.executeFaceMove(face, clockwise);
            });
        });

        // Wide move buttons
        document.querySelectorAll('.wide-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const face = e.target.dataset.face;
                const clockwise = e.target.dataset.clockwise === 'true';
                this.executeWideMove(face, clockwise);
            });
        });

        // Action buttons
        document.getElementById('manual-scramble')?.addEventListener('click', () => {
            this.manualScramble();
        });

        document.getElementById('auto-scramble')?.addEventListener('click', () => {
            this.autoScramble();
        });

        document.getElementById('solve-cube')?.addEventListener('click', () => {
            this.solveCube();
        });

        document.getElementById('reset-cube')?.addEventListener('click', () => {
            this.resetCube();
        });

        // Auto-rotate toggle
        document.getElementById('toggle-auto-rotate')?.addEventListener('click', () => {
            const isEnabled = this.cube3D.toggleAutoRotation();
            const button = document.getElementById('toggle-auto-rotate');
            button.textContent = isEnabled ? '🔄 Auto Rotate' : '⏸️ Auto Rotate';
            button.style.opacity = isEnabled ? '1' : '0.6';
        });

        // Manual scramble input
        document.getElementById('apply-manual-scramble')?.addEventListener('click', () => {
            this.applyManualScramble();
        });

        // Allow Enter key in manual scramble input
        document.getElementById('manual-scramble-input')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.applyManualScramble();
            }
        });

        // Speed control buttons
        document.querySelectorAll('.speed-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const speed = parseInt(e.target.dataset.speed);
                this.setAnimationSpeed(speed);
            });
        });

        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });
    }

    changeSize(newSize) {
        console.log(`Changing cube size to ${newSize}x${newSize}`);
        
        // Update active button
        document.querySelectorAll('.size-btn').forEach(btn => {
            btn.classList.toggle('active', parseInt(btn.dataset.size) === newSize);
        });

        // Update cube state
        this.cubeState.changeSize(newSize);
        
        // Update visualizations
        this.cube3D.changeSize();
        this.cube2D.changeSize();

        // Show/hide wide controls for 4x4
        const wideControls = document.getElementById('wide-controls');
        if (wideControls) {
            wideControls.style.display = newSize === 4 ? 'block' : 'none';
        }

        // Update display
        this.updateDisplay();
    }

    handleOrientation(action) {
        console.log(`Orientation action: ${action}`);
        
        switch (action) {
            case 'left':
                this.cube3D.rotateLeft();
                break;
            case 'right':
                this.cube3D.rotateRight();
                break;
            case 'flip':
                this.cube3D.flip();
                break;
        }
        
        // Note: These actions don't change cube state, only visual orientation
        console.log('Visual orientation changed, cube state unchanged');
    }

    executeFaceMove(face, clockwise) {
        console.log(`Executing face move: ${face}${clockwise ? '' : "'"}`);

        // Execute move on cube state
        this.cubeState.executeMove(face, clockwise);

        // Update visualizations
        this.cube3D.refresh();
        this.cube2D.refresh();

        // Update display
        this.updateDisplay();

        // Check if solved
        if (this.cubeState.isSolved()) {
            this.showSolvedMessage();
        }
    }

    executeWideMove(face, clockwise) {
        console.log(`Executing wide move: ${face}${clockwise ? '' : "'"}`);

        if (this.cubeState.size !== 4) {
            console.warn('Wide moves are only supported for 4x4 cubes');
            return;
        }

        // Execute wide move (affects two layers)
        this.cubeState.executeWideMove(face, clockwise);

        // Update visualizations
        this.cube3D.refresh();
        this.cube2D.refresh();

        // Update display
        this.updateDisplay();

        // Check if solved
        if (this.cubeState.isSolved()) {
            this.showSolvedMessage();
        }
    }

    manualScramble() {
        console.log('Manual scramble requested');
        
        // Generate and display scramble sequence
        const scramble = this.cubeState.generateScramble(15);
        console.log('Scramble sequence:', scramble);
        
        // Show scramble to user and let them apply it manually
        const solutionDisplay = document.getElementById('solution-moves');
        if (solutionDisplay) {
            solutionDisplay.innerHTML = `<strong>Manual Scramble:</strong><br>${scramble}<br><em>Apply these moves manually using the face buttons</em>`;
        }
    }

    async autoScramble() {
        console.log('Auto scramble requested');

        // Disable scramble button during animation
        const scrambleBtn = document.getElementById('auto-scramble');
        if (scrambleBtn) {
            scrambleBtn.disabled = true;
            scrambleBtn.textContent = '🎲 Scrambling...';
        }

        try {
            // Generate scramble
            const scramble = this.cubeState.generateScramble(20);
            console.log('Auto scramble sequence:', scramble);

            // Show scramble sequence immediately
            const solutionDisplay = document.getElementById('solution-moves');
            if (solutionDisplay) {
                solutionDisplay.innerHTML = `<strong>Applying Scramble:</strong><br>${scramble}<br><em>Watch each move...</em>`;
            }

            // Apply scramble with animation using current speed setting
            await this.applyScrambleWithAnimation(scramble, this.animationSpeed);

            // Update final display
            if (solutionDisplay) {
                solutionDisplay.innerHTML = `<strong>Applied Scramble:</strong><br>${scramble}`;
            }

        } finally {
            // Re-enable scramble button
            if (scrambleBtn) {
                scrambleBtn.disabled = false;
                scrambleBtn.textContent = '🎲 Auto Scramble';
            }
        }
    }

    async applyScrambleWithAnimation(scrambleString, delay = 500) {
        console.log('Applying scramble with animation:', scrambleString);

        try {
            // Parse scramble into individual moves
            const moves = scrambleString.split(' ').filter(move => move.trim());
            const solutionDisplay = document.getElementById('solution-moves');

            // Validate cube state before starting
            if (!this.cubeState.validateCubeState()) {
                throw new Error('Cube state is invalid before scramble');
            }

            for (let i = 0; i < moves.length; i++) {
                const move = moves[i].trim();
                if (!move) continue;

                console.log(`Applying animated move ${i + 1}/${moves.length}: ${move}`);

                // Update display to show current move
                if (solutionDisplay) {
                    const movesWithHighlight = moves.map((m, index) => {
                        if (index === i) {
                            return `<span style="background-color: #ffeb3b; padding: 2px 4px; border-radius: 3px; font-weight: bold;">${m}</span>`;
                        }
                        return m;
                    }).join(' ');

                    solutionDisplay.innerHTML = `<strong>Applying Scramble:</strong><br>${movesWithHighlight}<br><em>Current move: ${move} (${i + 1}/${moves.length})</em>`;
                }

                try {
                    // Parse the move - only single moves allowed
                    const face = move[0].toUpperCase();
                    const clockwise = !move.includes("'");

                    // Validate face
                    if (!['U', 'D', 'F', 'B', 'R', 'L'].includes(face)) {
                        console.warn(`Invalid face in move: ${move}, skipping`);
                        continue;
                    }

                    // Apply single move with visual updates and validation
                    this.cubeState.executeMove(face, clockwise);
                    this.cube3D.refresh();
                    this.cube2D.refresh();
                    this.updateDisplay();

                    console.log(`Move ${face}${clockwise ? '' : "'"} applied with animation`);

                    // Validate cube state after each move
                    if (!this.cubeState.validateCubeState()) {
                        throw new Error(`Cube state became invalid after move ${move}`);
                    }

                    // Wait before next move (except for the last move)
                    if (i < moves.length - 1) {
                        await this.sleep(delay);
                    }

                } catch (error) {
                    console.error(`CRITICAL ERROR applying animated move ${move}:`, error);
                    throw error; // Stop scramble on any error
                }
            }

            console.log('Animated scramble application completed successfully');

        } catch (error) {
            console.error('Animated scramble failed:', error);
            throw error;
        }
    }

    // Helper method for delays
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Set animation speed and update UI
    setAnimationSpeed(speed) {
        this.animationSpeed = speed;
        console.log(`Animation speed set to ${speed}ms`);

        // Update active button
        document.querySelectorAll('.speed-btn').forEach(btn => {
            btn.classList.toggle('active', parseInt(btn.dataset.speed) === speed);
        });
    }

    applyManualScramble() {
        const input = document.getElementById('manual-scramble-input');
        const movesString = input.value.trim();

        if (!movesString) {
            alert('Please enter some moves first!');
            return;
        }

        console.log('Applying manual scramble:', movesString);

        try {
            // Parse and validate moves
            const moves = this.parseMovesString(movesString);

            if (moves.length === 0) {
                alert('No valid moves found!');
                return;
            }

            // Apply each move
            let totalMoves = 0;
            moves.forEach(move => {
                if (Array.isArray(move)) {
                    // Handle double moves (R2 = two R moves)
                    move.forEach(m => {
                        this.cubeState.executeMove(m.face, m.clockwise);
                        totalMoves++;
                    });
                } else {
                    this.cubeState.executeMove(move.face, move.clockwise);
                    totalMoves++;
                }
            });

            // Update visualizations
            this.cube3D.refresh();
            this.cube2D.refresh();
            this.updateDisplay();

            // Show applied moves
            const solutionDisplay = document.getElementById('solution-moves');
            if (solutionDisplay) {
                solutionDisplay.innerHTML = `<strong>Applied Manual Moves:</strong><br>${movesString}<br><em>${totalMoves} moves applied</em>`;
            }

            // Clear input
            input.value = '';

            console.log(`Applied ${totalMoves} moves successfully`);

        } catch (error) {
            console.error('Error applying manual scramble:', error);
            alert(`Error applying moves: ${error.message}`);
        }
    }

    parseMovesString(movesString) {
        // Split by spaces and filter out empty strings
        const moveTokens = movesString.toUpperCase().split(/\s+/).filter(token => token.length > 0);
        const moves = [];

        for (const token of moveTokens) {
            const move = this.parseMove(token);
            if (move) {
                moves.push(move);
            } else {
                throw new Error(`Invalid move: ${token}`);
            }
        }

        return moves;
    }

    parseMove(moveToken) {
        // Handle standard moves: R, R', R2, U, U', U2, etc.
        // Also handle wide moves: Rw, Uw, etc. (for 4x4)
        const match = moveToken.match(/^([RLUDFB]w?)([']|2)?$/);

        if (!match) {
            return null;
        }

        const face = match[1];
        const modifier = match[2];

        // For now, treat wide moves (Rw, Uw, etc.) as regular moves for 3x3
        // TODO: Implement proper wide move support for 4x4
        const baseFace = face.replace('w', '');

        if (modifier === '2') {
            // Double turn - return array of two moves
            return [
                { face: baseFace, clockwise: true },
                { face: baseFace, clockwise: true }
            ];
        } else {
            // Single turn
            const clockwise = modifier !== "'";
            return { face: baseFace, clockwise };
        }
    }

    async solveCube() {
        console.log('Solve cube requested');

        if (this.cubeState.isSolved()) {
            alert('Cube is already solved!');
            return;
        }

        const solutionDisplay = document.getElementById('solution-moves');
        const solveBtn = document.getElementById('solve-cube');

        try {
            // Disable solve button during solving
            if (solveBtn) {
                solveBtn.disabled = true;
                solveBtn.textContent = '🧩 Solving...';
            }

            // Validate cube state before solving
            if (!this.cubeState.validateCubeState()) {
                throw new Error('Cube state is invalid - cannot solve');
            }

            // Show loading
            if (solutionDisplay) {
                solutionDisplay.innerHTML = '<strong>Validating cube state...</strong>';
            }

            // Get Kociemba string for validation
            const kociembaString = this.cubeState.getKociembaString();
            if (!kociembaString) {
                throw new Error('Cannot generate valid Kociemba string from current cube state');
            }

            console.log('Cube state validated, sending to solver...');
            if (solutionDisplay) {
                solutionDisplay.innerHTML = '<strong>Solving cube...</strong>';
            }

            // Get solution from server
            const solution = await this.getSolution();

            if (solution) {
                console.log('Solution received:', solution);

                // Only apply solution if it's not empty
                if (solution.trim()) {
                    if (solutionDisplay) {
                        solutionDisplay.innerHTML = '<strong>Applying solution...</strong>';
                    }

                    // Apply solution with validation
                    this.cubeState.applySolution(solution);

                    // Update visualizations
                    this.cube3D.refresh();
                    this.cube2D.refresh();
                    this.updateDisplay();

                    // Show solution
                    if (solutionDisplay) {
                        solutionDisplay.innerHTML = `<strong>Solution Applied:</strong><br>${solution}`;
                    }

                    // Verify cube is solved
                    if (this.cubeState.isSolved()) {
                        this.showSolvedMessage();
                        console.log('✓ Cube solved successfully!');
                    } else {
                        console.warn('Solution applied but cube is not solved - possible error');
                        if (solutionDisplay) {
                            solutionDisplay.innerHTML += '<br><em style="color: orange;">Warning: Cube may not be fully solved</em>';
                        }
                    }
                } else {
                    // Empty solution
                    if (solutionDisplay) {
                        solutionDisplay.innerHTML = `<strong>Info:</strong> Solver returned empty solution - cube may already be solved`;
                    }
                }
            } else {
                throw new Error('No solution received from server');
            }

        } catch (error) {
            console.error('SOLVE FAILED:', error);
            if (solutionDisplay) {
                solutionDisplay.innerHTML = `<strong>Solve Error:</strong><br>${error.message}`;
            }
        } finally {
            // Re-enable solve button
            if (solveBtn) {
                solveBtn.disabled = false;
                solveBtn.textContent = '🧩 Solve Cube';
            }
        }
    }

    async getSolution() {
        try {
            let cubeString;

            // For 3x3 cubes, use Kociemba format
            if (this.cubeState.size === 3) {
                cubeString = this.cubeState.getKociembaString();
                if (!cubeString) {
                    throw new Error('Cannot convert cube to Kociemba format - cube state may be invalid');
                }
                console.log('Using Kociemba string for 3x3 solve:', cubeString);
            } else {
                // For 2x2 and 4x4, use the internal cube string format
                cubeString = this.cubeState.cubeString;
                console.log(`Using internal string for ${this.cubeState.size}x${this.cubeState.size} solve:`, cubeString.substring(0, 20) + '...');
            }

            console.log('Sending solve request to server...');
            const response = await fetch('http://localhost:5000/solve', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ cube_string: cubeString })
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Server response error:', response.status, errorText);
                throw new Error(`Server error ${response.status}: ${errorText}`);
            }

            const data = await response.json();
            console.log('Server response:', data);

            // Handle different response formats
            if (data.error) {
                throw new Error(`Solver error: ${data.error}`);
            }

            const solution = data.solution || '';
            console.log('Solution extracted:', solution);
            return solution;

        } catch (error) {
            console.error('getSolution failed:', error);
            throw error;
        }
    }

    resetCube() {
        console.log('Reset cube requested');
        
        this.cubeState.resetToSolved();
        
        // Update visualizations
        this.cube3D.refresh();
        this.cube2D.refresh();
        
        // Update display
        this.updateDisplay();
        
        // Clear solution display
        const solutionDisplay = document.getElementById('solution-moves');
        if (solutionDisplay) {
            solutionDisplay.innerHTML = '';
        }
    }

    handleKeyboard(event) {
        // Keyboard shortcuts for face moves
        const keyMap = {
            'f': () => this.executeFaceMove('F', true),
            'F': () => this.executeFaceMove('F', false),
            'r': () => this.executeFaceMove('R', true),
            'R': () => this.executeFaceMove('R', false),
            'u': () => this.executeFaceMove('U', true),
            'U': () => this.executeFaceMove('U', false),
            'd': () => this.executeFaceMove('D', true),
            'D': () => this.executeFaceMove('D', false),
            'l': () => this.executeFaceMove('L', true),
            'L': () => this.executeFaceMove('L', false),
            'b': () => this.executeFaceMove('B', true),
            'B': () => this.executeFaceMove('B', false),
        };

        const handler = keyMap[event.key];
        if (handler) {
            event.preventDefault();
            handler();
        }
    }

    updateDisplay() {
        // Update cube string display
        const stringDisplay = document.getElementById('cube-string-display');
        if (stringDisplay) {
            const cubeString = this.cubeState.cubeString;
            stringDisplay.textContent = cubeString;
        }
    }

    showSolvedMessage() {
        console.log('Cube is solved!');
        
        // Visual feedback
        setTimeout(() => {
            alert('🎉 Congratulations! Cube is solved! 🎉');
        }, 100);
    }
}

// Export for use in other modules
window.Controls = Controls;
