/**
 * Clean Controls Handler
 * Manages user interactions and coordinates between components
 */

class Controls {
    constructor(cubeState, cube3D, cube2D) {
        this.cubeState = cubeState;
        this.cube3D = cube3D;
        this.cube2D = cube2D;
        this.animationSpeed = 400; // Default animation speed in milliseconds

        this.setupEventListeners();
    }

    setupEventListeners() {
        // Size selector buttons
        document.querySelectorAll('.size-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const size = parseInt(e.target.dataset.size);
                this.changeSize(size);
            });
        });

        // Orientation controls (visual only)
        document.querySelectorAll('.orientation-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                this.handleOrientation(action);
            });
        });

        // Face rotation buttons
        document.querySelectorAll('.face-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const face = e.target.dataset.face;
                const clockwise = e.target.dataset.clockwise === 'true';
                this.executeFaceMove(face, clockwise);
            });
        });

        // Wide move buttons
        document.querySelectorAll('.wide-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const face = e.target.dataset.face;
                const clockwise = e.target.dataset.clockwise === 'true';
                this.executeWideMove(face, clockwise);
            });
        });

        // Action buttons
        document.getElementById('manual-scramble')?.addEventListener('click', () => {
            this.manualScramble();
        });

        document.getElementById('auto-scramble')?.addEventListener('click', () => {
            this.autoScramble();
        });

        document.getElementById('solve-cube')?.addEventListener('click', () => {
            this.solveCube();
        });

        document.getElementById('reset-cube')?.addEventListener('click', () => {
            this.resetCube();
        });

        // Auto-rotate toggle
        document.getElementById('toggle-auto-rotate')?.addEventListener('click', () => {
            const isEnabled = this.cube3D.toggleAutoRotation();
            const button = document.getElementById('toggle-auto-rotate');
            button.textContent = isEnabled ? '🔄 Auto Rotate' : '⏸️ Auto Rotate';
            button.style.opacity = isEnabled ? '1' : '0.6';
        });

        // Manual scramble input
        document.getElementById('apply-manual-scramble')?.addEventListener('click', () => {
            this.applyManualScramble();
        });

        // Allow Enter key in manual scramble input
        document.getElementById('manual-scramble-input')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.applyManualScramble();
            }
        });

        // Speed control buttons
        document.querySelectorAll('.speed-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const speed = parseInt(e.target.dataset.speed);
                this.setAnimationSpeed(speed);
            });
        });

        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });
    }

    changeSize(newSize) {
        console.log(`Changing cube size to ${newSize}x${newSize}`);
        
        // Update active button
        document.querySelectorAll('.size-btn').forEach(btn => {
            btn.classList.toggle('active', parseInt(btn.dataset.size) === newSize);
        });

        // Update cube state
        this.cubeState.changeSize(newSize);
        
        // Update visualizations
        this.cube3D.changeSize();
        this.cube2D.changeSize();

        // Show/hide wide controls for 4x4
        const wideControls = document.getElementById('wide-controls');
        if (wideControls) {
            wideControls.style.display = newSize === 4 ? 'block' : 'none';
        }

        // Update display
        this.updateDisplay();
    }

    handleOrientation(action) {
        console.log(`Orientation action: ${action}`);
        
        switch (action) {
            case 'left':
                this.cube3D.rotateLeft();
                break;
            case 'right':
                this.cube3D.rotateRight();
                break;
            case 'flip':
                this.cube3D.flip();
                break;
        }
        
        // Note: These actions don't change cube state, only visual orientation
        console.log('Visual orientation changed, cube state unchanged');
    }

    async executeFaceMove(face, clockwise) {
        console.log(`Executing face move: ${face}${clockwise ? '' : "'"}`);

        try {
            // Disable controls during move execution
            this.setControlsEnabled(false);

            // Animate the 3D rotation first (visual only)
            if (this.cube3D.animateFaceRotation) {
                await this.cube3D.animateFaceRotation(face, clockwise, this.animationSpeed);
            }

            // Execute move on cube state
            this.cubeState.executeMove(face, clockwise);

            // Update visualizations
            this.cube3D.refresh();
            this.cube2D.refresh();

            // Update display
            this.updateDisplay();

            // Check if solved
            if (this.cubeState.isSolved()) {
                this.showSolvedMessage();
            }

        } catch (error) {
            console.error('Error executing face move:', error);
            this.showError('Failed to execute move: ' + error.message);
        } finally {
            // Re-enable controls
            this.setControlsEnabled(true);
        }
    }

    setControlsEnabled(enabled) {
        // Disable/enable all face move buttons
        document.querySelectorAll('.face-btn, .wide-btn').forEach(btn => {
            btn.disabled = !enabled;
            btn.style.opacity = enabled ? '1' : '0.6';
        });

        // Disable/enable action buttons
        document.querySelectorAll('#auto-scramble, #solve-cube, #reset-cube').forEach(btn => {
            if (btn) {
                btn.disabled = !enabled;
                btn.style.opacity = enabled ? '1' : '0.6';
            }
        });
    }

    executeWideMove(face, clockwise) {
        console.log(`Executing wide move: ${face}${clockwise ? '' : "'"}`);

        if (this.cubeState.size !== 4) {
            console.warn('Wide moves are only supported for 4x4 cubes');
            return;
        }

        // Execute wide move (affects two layers)
        this.cubeState.executeWideMove(face, clockwise);

        // Update visualizations
        this.cube3D.refresh();
        this.cube2D.refresh();

        // Update display
        this.updateDisplay();

        // Check if solved
        if (this.cubeState.isSolved()) {
            this.showSolvedMessage();
        }
    }

    manualScramble() {
        console.log('Manual scramble requested');
        
        // Generate and display scramble sequence
        const scramble = this.cubeState.generateScramble(15);
        console.log('Scramble sequence:', scramble);
        
        // Show scramble to user and let them apply it manually
        const solutionDisplay = document.getElementById('solution-moves');
        if (solutionDisplay) {
            solutionDisplay.innerHTML = `<strong>Manual Scramble:</strong><br>${scramble}<br><em>Apply these moves manually using the face buttons</em>`;
        }
    }

    async autoScramble() {
        console.log('Auto scramble requested');

        try {
            // Disable all controls during scrambling
            this.setControlsEnabled(false);

            const scrambleBtn = document.getElementById('auto-scramble');
            if (scrambleBtn) {
                scrambleBtn.textContent = '🎲 Scrambling...';
            }

            // Generate scramble sequence
            const scrambleSequence = this.cubeState.generateScramble(15);
            const moves = scrambleSequence.split(' ');

            console.log('Executing scramble:', scrambleSequence);

            // Execute each move with animation
            for (const move of moves) {
                if (move.trim()) {
                    const face = move.charAt(0);
                    const clockwise = !move.includes("'");

                    // Animate the move
                    if (this.cube3D.animateFaceRotation) {
                        await this.cube3D.animateFaceRotation(face, clockwise, 200); // Faster for scrambling
                    }

                    // Execute the move on cube state
                    this.cubeState.executeMove(face, clockwise);

                    // Small delay between moves for better visual effect
                    await new Promise(resolve => setTimeout(resolve, 50));
                }
            }

            // Update visualizations after all moves
            this.cube3D.refresh();
            this.cube2D.refresh();
            this.updateDisplay();

            // Show scramble sequence to user
            const solutionDisplay = document.getElementById('solution-moves');
            if (solutionDisplay) {
                solutionDisplay.innerHTML = `<strong>Scramble Applied:</strong><br>${scrambleSequence}`;
            }

            console.log('Scramble completed successfully');

        } catch (error) {
            console.error('Error during scrambling:', error);
            this.showError('Scrambling failed: ' + error.message);
        } finally {
            // Re-enable controls
            this.setControlsEnabled(true);

            const scrambleBtn = document.getElementById('auto-scramble');
            if (scrambleBtn) {
                scrambleBtn.textContent = '🎲 Auto Scramble';
            }
        }
    }

    showError(message) {
        // Create error notification
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 15px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 300px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        `;
        errorDiv.textContent = message;

        document.body.appendChild(errorDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

        try {
            // Generate scramble
            const scramble = this.cubeState.generateScramble(20);
            console.log('Auto scramble sequence:', scramble);

            // Show scramble sequence immediately
            const solutionDisplay = document.getElementById('solution-moves');
            if (solutionDisplay) {
                solutionDisplay.innerHTML = `<strong>Applying Scramble:</strong><br>${scramble}<br><em>Watch each move...</em>`;
            }

            // Apply scramble with animation using current speed setting
            await this.applyScrambleWithAnimation(scramble, this.animationSpeed);

            // Update final display
            if (solutionDisplay) {
                solutionDisplay.innerHTML = `<strong>Applied Scramble:</strong><br>${scramble}`;
            }

        } finally {
            // Re-enable scramble button
            if (scrambleBtn) {
                scrambleBtn.disabled = false;
                scrambleBtn.textContent = '🎲 Auto Scramble';
            }
        }
    }

    async applyScrambleWithAnimation(scrambleString, delay = 500) {
        console.log('Applying scramble with animation:', scrambleString);

        try {
            // Parse scramble into individual moves
            const moves = scrambleString.split(' ').filter(move => move.trim());
            const solutionDisplay = document.getElementById('solution-moves');

            // Validate cube state before starting
            if (!this.cubeState.validateCubeState()) {
                throw new Error('Cube state is invalid before scramble');
            }

            for (let i = 0; i < moves.length; i++) {
                const move = moves[i].trim();
                if (!move) continue;

                console.log(`Applying animated move ${i + 1}/${moves.length}: ${move}`);

                // Update display to show current move
                if (solutionDisplay) {
                    const movesWithHighlight = moves.map((m, index) => {
                        if (index === i) {
                            return `<span style="background-color: #ffeb3b; padding: 2px 4px; border-radius: 3px; font-weight: bold;">${m}</span>`;
                        }
                        return m;
                    }).join(' ');

                    solutionDisplay.innerHTML = `<strong>Applying Scramble:</strong><br>${movesWithHighlight}<br><em>Current move: ${move} (${i + 1}/${moves.length})</em>`;
                }

                try {
                    // Parse the move - only single moves allowed
                    const face = move[0].toUpperCase();
                    const clockwise = !move.includes("'");

                    // Validate face
                    if (!['U', 'D', 'F', 'B', 'R', 'L'].includes(face)) {
                        console.warn(`Invalid face in move: ${move}, skipping`);
                        continue;
                    }

                    // Apply single move with visual updates and validation
                    this.cubeState.executeMove(face, clockwise);
                    this.cube3D.refresh();
                    this.cube2D.refresh();
                    this.updateDisplay();

                    console.log(`Move ${face}${clockwise ? '' : "'"} applied with animation`);

                    // Validate cube state after each move
                    if (!this.cubeState.validateCubeState()) {
                        throw new Error(`Cube state became invalid after move ${move}`);
                    }

                    // Wait before next move (except for the last move)
                    if (i < moves.length - 1) {
                        await this.sleep(delay);
                    }

                } catch (error) {
                    console.error(`CRITICAL ERROR applying animated move ${move}:`, error);
                    throw error; // Stop scramble on any error
                }
            }

            console.log('Animated scramble application completed successfully');

        } catch (error) {
            console.error('Animated scramble failed:', error);
            throw error;
        }
    }

    // Helper method for delays
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Set animation speed and update UI
    setAnimationSpeed(speed) {
        this.animationSpeed = speed;
        console.log(`Animation speed set to ${speed}ms`);

        // Update active button
        document.querySelectorAll('.speed-btn').forEach(btn => {
            btn.classList.toggle('active', parseInt(btn.dataset.speed) === speed);
        });
    }

    applyManualScramble() {
        const input = document.getElementById('manual-scramble-input');
        const movesString = input.value.trim();

        if (!movesString) {
            alert('Please enter some moves first!');
            return;
        }

        console.log('Applying manual scramble:', movesString);

        try {
            // Parse and validate moves
            const moves = this.parseMovesString(movesString);

            if (moves.length === 0) {
                alert('No valid moves found!');
                return;
            }

            // Apply each move
            let totalMoves = 0;
            moves.forEach(move => {
                if (Array.isArray(move)) {
                    // Handle double moves (R2 = two R moves)
                    move.forEach(m => {
                        this.cubeState.executeMove(m.face, m.clockwise);
                        totalMoves++;
                    });
                } else {
                    this.cubeState.executeMove(move.face, move.clockwise);
                    totalMoves++;
                }
            });

            // Update visualizations
            this.cube3D.refresh();
            this.cube2D.refresh();
            this.updateDisplay();

            // Show applied moves
            const solutionDisplay = document.getElementById('solution-moves');
            if (solutionDisplay) {
                solutionDisplay.innerHTML = `<strong>Applied Manual Moves:</strong><br>${movesString}<br><em>${totalMoves} moves applied</em>`;
            }

            // Clear input
            input.value = '';

            console.log(`Applied ${totalMoves} moves successfully`);

        } catch (error) {
            console.error('Error applying manual scramble:', error);
            alert(`Error applying moves: ${error.message}`);
        }
    }

    parseMovesString(movesString) {
        // Split by spaces and filter out empty strings
        const moveTokens = movesString.toUpperCase().split(/\s+/).filter(token => token.length > 0);
        const moves = [];

        for (const token of moveTokens) {
            const move = this.parseMove(token);
            if (move) {
                moves.push(move);
            } else {
                throw new Error(`Invalid move: ${token}`);
            }
        }

        return moves;
    }

    parseMove(moveToken) {
        // Handle standard moves: R, R', R2, U, U', U2, etc.
        // Also handle wide moves: Rw, Uw, etc. (for 4x4)
        const match = moveToken.match(/^([RLUDFB]w?)([']|2)?$/);

        if (!match) {
            return null;
        }

        const face = match[1];
        const modifier = match[2];

        // For now, treat wide moves (Rw, Uw, etc.) as regular moves for 3x3
        // TODO: Implement proper wide move support for 4x4
        const baseFace = face.replace('w', '');

        if (modifier === '2') {
            // Double turn - return array of two moves
            return [
                { face: baseFace, clockwise: true },
                { face: baseFace, clockwise: true }
            ];
        } else {
            // Single turn
            const clockwise = modifier !== "'";
            return { face: baseFace, clockwise };
        }
    }

    async solveCube() {
        console.log('🧠 Solve cube requested');

        if (this.cubeState.isSolved()) {
            this.showMessage('Cube is already solved! 🎉', 'success');
            return;
        }

        // Disable controls during solving
        this.setControlsEnabled(false);

        const solveBtn = document.getElementById('solve-cube');
        if (solveBtn) {
            solveBtn.textContent = '🧠 Solving...';
        }

        const solutionDisplay = document.getElementById('solution-moves');
        if (solutionDisplay) {
            solutionDisplay.innerHTML = '<strong>🔍 Analyzing cube...</strong>';
        }

        try {
            // Use the enhanced solver with Kociemba integration
            const solver = new Solver();
            const result = await solver.solve(this.cubeState, 20, 5);

            if (result.success) {
                console.log('✅ Solution found:', result);

                if (solutionDisplay) {
                    solutionDisplay.innerHTML = `
                        <div class="solution-result">
                            <strong>✅ Solution Found!</strong><br>
                            <strong>Algorithm:</strong> ${result.algorithm}<br>
                            <strong>Length:</strong> ${result.length} moves<br>
                            <strong>Solution:</strong><br>
                            <div class="solution-moves">${result.solution}</div>
                            <button onclick="window.app.controls.applySolution('${result.solution}')"
                                    class="apply-solution-btn">
                                🎯 Apply Solution
                            </button>
                        </div>
                    `;
                }

                this.showMessage(`Solution found in ${result.length} moves using ${result.algorithm}!`, 'success');

            } else {
                console.error('❌ Solving failed:', result.error);

                if (solutionDisplay) {
                    solutionDisplay.innerHTML = `
                        <div class="solution-error">
                            <strong>❌ Solving Failed</strong><br>
                            <em>${result.error}</em><br><br>
                            <button onclick="window.app.controls.showKociembaInstructions()"
                                    class="setup-btn">
                                📋 Setup Kociemba Solver
                            </button>
                        </div>
                    `;
                }

                this.showMessage('Solving failed: ' + result.error, 'error');
            }

        } catch (error) {
            console.error('❌ Solving error:', error);

            if (solutionDisplay) {
                solutionDisplay.innerHTML = `
                    <div class="solution-error">
                        <strong>❌ Solving Error</strong><br>
                        <em>${error.message}</em>
                    </div>
                `;
            }

            this.showMessage('Solving error: ' + error.message, 'error');
        } finally {
            // Re-enable controls
            this.setControlsEnabled(true);

            if (solveBtn) {
                solveBtn.textContent = '🧠 Solve Cube';
            }
        }
    }

    async getSolution() {
        try {
            let cubeString;

            // For 3x3 cubes, use Kociemba format
            if (this.cubeState.size === 3) {
                cubeString = this.cubeState.getKociembaString();
                if (!cubeString) {
                    throw new Error('Cannot convert cube to Kociemba format - cube state may be invalid');
                }
                console.log('Using Kociemba string for 3x3 solve:', cubeString);
            } else {
                // For 2x2 and 4x4, use the internal cube string format
                cubeString = this.cubeState.cubeString;
                console.log(`Using internal string for ${this.cubeState.size}x${this.cubeState.size} solve:`, cubeString.substring(0, 20) + '...');
            }

            console.log('Sending solve request to server...');
            const response = await fetch('http://localhost:5000/solve', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ cube_string: cubeString })
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Server response error:', response.status, errorText);
                throw new Error(`Server error ${response.status}: ${errorText}`);
            }

            const data = await response.json();
            console.log('Server response:', data);

            // Handle different response formats
            if (data.error) {
                throw new Error(`Solver error: ${data.error}`);
            }

            const solution = data.solution || '';
            console.log('Solution extracted:', solution);
            return solution;

        } catch (error) {
            console.error('getSolution failed:', error);
            throw error;
        }
    }

    resetCube() {
        console.log('Reset cube requested');
        
        this.cubeState.resetToSolved();
        
        // Update visualizations
        this.cube3D.refresh();
        this.cube2D.refresh();
        
        // Update display
        this.updateDisplay();
        
        // Clear solution display
        const solutionDisplay = document.getElementById('solution-moves');
        if (solutionDisplay) {
            solutionDisplay.innerHTML = '';
        }
    }

    handleKeyboard(event) {
        // Keyboard shortcuts for face moves
        const keyMap = {
            'f': () => this.executeFaceMove('F', true),
            'F': () => this.executeFaceMove('F', false),
            'r': () => this.executeFaceMove('R', true),
            'R': () => this.executeFaceMove('R', false),
            'u': () => this.executeFaceMove('U', true),
            'U': () => this.executeFaceMove('U', false),
            'd': () => this.executeFaceMove('D', true),
            'D': () => this.executeFaceMove('D', false),
            'l': () => this.executeFaceMove('L', true),
            'L': () => this.executeFaceMove('L', false),
            'b': () => this.executeFaceMove('B', true),
            'B': () => this.executeFaceMove('B', false),
        };

        const handler = keyMap[event.key];
        if (handler) {
            event.preventDefault();
            handler();
        }
    }

    updateDisplay() {
        // Update cube string display
        const stringDisplay = document.getElementById('cube-string-display');
        if (stringDisplay) {
            const cubeString = this.cubeState.cubeString;
            stringDisplay.textContent = cubeString;
        }
    }

    showSolvedMessage() {
        console.log('Cube is solved!');

        // Visual feedback
        setTimeout(() => {
            alert('🎉 Congratulations! Cube is solved! 🎉');
        }, 100);
    }

    async applySolution(solutionString) {
        /**
         * Apply a solution string to the cube with animation
         */
        console.log('🎯 Applying solution:', solutionString);

        try {
            this.setControlsEnabled(false);

            const moves = solutionString.trim().split(/\s+/).filter(move => move.length > 0);
            console.log('Parsed moves:', moves);

            const solutionDisplay = document.getElementById('solution-moves');

            for (let i = 0; i < moves.length; i++) {
                const move = moves[i];
                console.log(`Applying move ${i + 1}/${moves.length}: ${move}`);

                // Update display to show current move
                if (solutionDisplay) {
                    const movesWithHighlight = moves.map((m, index) => {
                        if (index === i) {
                            return `<span style="background-color: #ffeb3b; padding: 2px 4px; border-radius: 3px; font-weight: bold;">${m}</span>`;
                        } else if (index < i) {
                            return `<span style="opacity: 0.5;">${m}</span>`;
                        } else {
                            return m;
                        }
                    }).join(' ');

                    solutionDisplay.innerHTML = `
                        <strong>🎯 Applying Solution (${i + 1}/${moves.length}):</strong><br>
                        <div class="solution-moves">${movesWithHighlight}</div>
                    `;
                }

                // Parse and execute move
                const { face, clockwise } = this.parseMove(move);
                await this.executeFaceMove(face, clockwise);

                // Small delay between moves
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Check if solved
            if (this.cubeState.isSolved()) {
                this.showSolvedMessage();
                if (solutionDisplay) {
                    solutionDisplay.innerHTML = `
                        <strong>🎉 Solution Applied Successfully!</strong><br>
                        <div class="solution-moves">${solutionString}</div>
                        <em>Cube is now solved!</em>
                    `;
                }
            } else {
                if (solutionDisplay) {
                    solutionDisplay.innerHTML = `
                        <strong>⚠️ Solution Applied</strong><br>
                        <div class="solution-moves">${solutionString}</div>
                        <em style="color: orange;">Warning: Cube may not be fully solved</em>
                    `;
                }
            }

        } catch (error) {
            console.error('❌ Error applying solution:', error);
            this.showMessage('Error applying solution: ' + error.message, 'error');
        } finally {
            this.setControlsEnabled(true);
        }
    }

    async showKociembaInstructions() {
        /**
         * Show instructions for setting up the Kociemba solver
         */
        const solver = new Solver();
        const instructions = await solver.getKociembaInstructions();

        const solutionDisplay = document.getElementById('solution-moves');
        if (solutionDisplay) {
            solutionDisplay.innerHTML = `
                <div class="kociemba-instructions">
                    <strong>📋 Kociemba Solver Setup</strong><br>
                    <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px; font-size: 12px; overflow-x: auto;">${instructions}</pre>
                    <button onclick="window.app.controls.checkSolverStatus()" class="check-status-btn">
                        🔍 Check Server Status
                    </button>
                </div>
            `;
        }
    }

    async checkSolverStatus() {
        /**
         * Check and display solver status
         */
        const solver = new Solver();
        const status = solver.getServerStatus();

        const solutionDisplay = document.getElementById('solution-moves');
        if (solutionDisplay) {
            solutionDisplay.innerHTML = `
                <div class="solver-status">
                    <strong>🔍 Solver Status</strong><br>
                    <strong>Kociemba Server:</strong> ${status.kociemba.isRunning ? '✅ Running' : '❌ Not Available'}<br>
                    <strong>URL:</strong> ${status.kociemba.url}<br>
                    <strong>Enabled:</strong> ${status.useKociemba ? '✅ Yes' : '❌ No'}<br>
                    <br>
                    ${!status.kociemba.isRunning ?
                        '<button onclick="window.app.controls.showKociembaInstructions()" class="setup-btn">📋 Setup Instructions</button>' :
                        '<em style="color: green;">Ready to solve cubes!</em>'
                    }
                </div>
            `;
        }
    }

    showMessage(message, type = 'info') {
        // Create message notification
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 1000;
            max-width: 350px;
            font-weight: 500;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            animation: slideIn 0.3s ease-out;
        `;

        // Set colors based on type
        switch (type) {
            case 'success':
                messageDiv.style.background = 'linear-gradient(45deg, #4CAF50, #45a049)';
                messageDiv.style.color = 'white';
                break;
            case 'error':
                messageDiv.style.background = 'linear-gradient(45deg, #f44336, #d32f2f)';
                messageDiv.style.color = 'white';
                break;
            case 'warning':
                messageDiv.style.background = 'linear-gradient(45deg, #ff9800, #f57c00)';
                messageDiv.style.color = 'white';
                break;
            default:
                messageDiv.style.background = 'linear-gradient(45deg, #2196F3, #1976D2)';
                messageDiv.style.color = 'white';
        }

        messageDiv.textContent = message;
        document.body.appendChild(messageDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }
        }, 5000);
    }
}

// Export for use in other modules
window.Controls = Controls;
