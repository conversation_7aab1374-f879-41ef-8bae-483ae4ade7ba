/**
 * Correct Rubik's Cube State Management
 * Based on standard cube mechanics with proper move implementations
 */

class CubeState {
    constructor(size = 3) {
        this.size = size;
        this.initializeState();
    }

    initializeState() {
        // Standard color scheme
        this.colors = {
            'U': 'white',   // Up (top)
            'D': 'yellow',  // Down (bottom)
            'F': 'green',   // Front
            'B': 'blue',    // Back
            'R': 'red',     // Right
            'L': 'orange'   // Left
        };

        this.colorToChar = {
            'white': 'W',
            'yellow': 'Y', 
            'green': 'G',
            'blue': 'B',
            'red': 'R',
            'orange': 'O'
        };

        this.charToColor = {
            'W': 'white', 'Y': 'yellow', 'G': 'green',
            'B': 'blue', 'R': 'red', 'O': 'orange'
        };

        this.resetToSolved();
    }

    resetToSolved() {
        const stickersPerFace = this.size * this.size;
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        
        this.cubeString = '';
        faceOrder.forEach(face => {
            const color = this.colors[face];
            const char = this.colorToChar[color];
            this.cubeString += char.repeat(stickersPerFace);
        });

        console.log(`Initialized ${this.size}x${this.size} cube:`, this.cubeString);
    }

    // Convert string to face arrays for easier manipulation
    stringToFaces() {
        const stickersPerFace = this.size * this.size;
        const faces = {};
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        
        faceOrder.forEach((face, faceIndex) => {
            const start = faceIndex * stickersPerFace;
            const faceString = this.cubeString.substring(start, start + stickersPerFace);
            
            faces[face] = [];
            for (let row = 0; row < this.size; row++) {
                faces[face][row] = [];
                for (let col = 0; col < this.size; col++) {
                    const index = row * this.size + col;
                    faces[face][row][col] = faceString[index];
                }
            }
        });
        
        return faces;
    }

    // Convert face arrays back to string
    facesToString(faces) {
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        let result = '';
        
        faceOrder.forEach(face => {
            for (let row = 0; row < this.size; row++) {
                for (let col = 0; col < this.size; col++) {
                    result += faces[face][row][col];
                }
            }
        });
        
        return result;
    }

    // Rotate a face 90 degrees clockwise
    rotateFaceClockwise(face) {
        const size = this.size;
        const rotated = Array(size).fill().map(() => Array(size));
        
        for (let row = 0; row < size; row++) {
            for (let col = 0; col < size; col++) {
                rotated[col][size - 1 - row] = face[row][col];
            }
        }
        
        return rotated;
    }

    // Rotate a face 90 degrees counterclockwise
    rotateFaceCounterclockwise(face) {
        const size = this.size;
        const rotated = Array(size).fill().map(() => Array(size));
        
        for (let row = 0; row < size; row++) {
            for (let col = 0; col < size; col++) {
                rotated[size - 1 - col][row] = face[row][col];
            }
        }
        
        return rotated;
    }

    // Get column from face
    getColumn(face, colIndex) {
        return face.map(row => row[colIndex]);
    }

    // Set column in face
    setColumn(face, colIndex, values) {
        for (let row = 0; row < this.size; row++) {
            face[row][colIndex] = values[row];
        }
    }

    // Execute a single move - CORRECTED IMPLEMENTATION
    executeMove(move, clockwise = true) {
        console.log(`Executing move: ${move}${clockwise ? '' : "'"}`);
        
        const faces = this.stringToFaces();
        
        // Rotate the face itself
        if (clockwise) {
            faces[move] = this.rotateFaceClockwise(faces[move]);
        } else {
            faces[move] = this.rotateFaceCounterclockwise(faces[move]);
        }
        
        // Update adjacent edges - CORRECT IMPLEMENTATION
        this.updateAdjacentEdges(faces, move, clockwise);
        
        // Convert back to string
        this.cubeString = this.facesToString(faces);
        
        console.log('Move completed successfully');
        return this.cubeString;
    }

    // Update adjacent edges - COMPLETELY REWRITTEN WITH CORRECT LOGIC
    updateAdjacentEdges(faces, move, clockwise) {
        const size = this.size;
        
        // Define the correct edge cycles for each face
        const edgeCycles = {
            'U': {
                // U face: Front top -> Right top -> Back top -> Left top -> Front top
                edges: [
                    { face: 'F', getter: () => faces.F[0].slice(), setter: (vals) => faces.F[0] = vals },
                    { face: 'R', getter: () => faces.R[0].slice(), setter: (vals) => faces.R[0] = vals },
                    { face: 'B', getter: () => faces.B[0].slice(), setter: (vals) => faces.B[0] = vals },
                    { face: 'L', getter: () => faces.L[0].slice(), setter: (vals) => faces.L[0] = vals }
                ]
            },
            'D': {
                // D face: Front bottom -> Left bottom -> Back bottom -> Right bottom -> Front bottom
                edges: [
                    { face: 'F', getter: () => faces.F[size-1].slice(), setter: (vals) => faces.F[size-1] = vals },
                    { face: 'L', getter: () => faces.L[size-1].slice(), setter: (vals) => faces.L[size-1] = vals },
                    { face: 'B', getter: () => faces.B[size-1].slice(), setter: (vals) => faces.B[size-1] = vals },
                    { face: 'R', getter: () => faces.R[size-1].slice(), setter: (vals) => faces.R[size-1] = vals }
                ]
            },
            'F': {
                // F face: Up bottom -> Right left -> Down top (reversed) -> Left right (reversed)
                edges: [
                    { face: 'U', getter: () => faces.U[size-1].slice(), setter: (vals) => faces.U[size-1] = vals },
                    { face: 'R', getter: () => this.getColumn(faces.R, 0), setter: (vals) => this.setColumn(faces.R, 0, vals) },
                    { face: 'D', getter: () => faces.D[0].slice().reverse(), setter: (vals) => faces.D[0] = vals.reverse() },
                    { face: 'L', getter: () => this.getColumn(faces.L, size-1).reverse(), setter: (vals) => this.setColumn(faces.L, size-1, vals.reverse()) }
                ]
            },
            'B': {
                // B face: Up top (reversed) -> Left left -> Down bottom -> Right right (reversed)
                edges: [
                    { face: 'U', getter: () => faces.U[0].slice().reverse(), setter: (vals) => faces.U[0] = vals.reverse() },
                    { face: 'L', getter: () => this.getColumn(faces.L, 0), setter: (vals) => this.setColumn(faces.L, 0, vals) },
                    { face: 'D', getter: () => faces.D[size-1].slice(), setter: (vals) => faces.D[size-1] = vals },
                    { face: 'R', getter: () => this.getColumn(faces.R, size-1).reverse(), setter: (vals) => this.setColumn(faces.R, size-1, vals.reverse()) }
                ]
            },
            'R': {
                // R face: Up right -> Back left (reversed) -> Down right -> Front right
                edges: [
                    { face: 'U', getter: () => this.getColumn(faces.U, size-1), setter: (vals) => this.setColumn(faces.U, size-1, vals) },
                    { face: 'B', getter: () => this.getColumn(faces.B, 0).reverse(), setter: (vals) => this.setColumn(faces.B, 0, vals.reverse()) },
                    { face: 'D', getter: () => this.getColumn(faces.D, size-1), setter: (vals) => this.setColumn(faces.D, size-1, vals) },
                    { face: 'F', getter: () => this.getColumn(faces.F, size-1), setter: (vals) => this.setColumn(faces.F, size-1, vals) }
                ]
            },
            'L': {
                // L face: Up left -> Front left -> Down left -> Back right (reversed)
                edges: [
                    { face: 'U', getter: () => this.getColumn(faces.U, 0), setter: (vals) => this.setColumn(faces.U, 0, vals) },
                    { face: 'F', getter: () => this.getColumn(faces.F, 0), setter: (vals) => this.setColumn(faces.F, 0, vals) },
                    { face: 'D', getter: () => this.getColumn(faces.D, 0), setter: (vals) => this.setColumn(faces.D, 0, vals) },
                    { face: 'B', getter: () => this.getColumn(faces.B, size-1).reverse(), setter: (vals) => this.setColumn(faces.B, size-1, vals.reverse()) }
                ]
            }
        };
        
        const cycle = edgeCycles[move];
        if (!cycle) return;
        
        // Get current edge values
        const edgeValues = cycle.edges.map(edge => edge.getter());
        
        // Cycle the edges
        if (clockwise) {
            // Clockwise: 0->1, 1->2, 2->3, 3->0
            const temp = edgeValues[0];
            for (let i = 0; i < 3; i++) {
                edgeValues[i] = edgeValues[i + 1];
            }
            edgeValues[3] = temp;
        } else {
            // Counterclockwise: 0->3, 1->0, 2->1, 3->2
            const temp = edgeValues[3];
            for (let i = 3; i > 0; i--) {
                edgeValues[i] = edgeValues[i - 1];
            }
            edgeValues[0] = temp;
        }
        
        // Set the new edge values
        cycle.edges.forEach((edge, i) => edge.setter(edgeValues[i]));
    }

    // Parse and execute move sequence
    applyMoves(moveSequence) {
        const moves = moveSequence.trim().split(/\s+/).filter(move => move);

        moves.forEach(move => {
            const cleanMove = move.trim();
            if (!cleanMove) return;

            let face = cleanMove[0];
            let clockwise = true;

            if (cleanMove.includes("'") || cleanMove.includes("'")) {
                clockwise = false;
            } else if (cleanMove.includes('2')) {
                // Double move
                this.executeMove(face, true);
                this.executeMove(face, true);
                return;
            }

            this.executeMove(face, clockwise);
        });

        return this.cubeString;
    }

    // Generate scramble
    generateScramble(length = 20) {
        const faces = ['U', 'D', 'F', 'B', 'R', 'L'];
        const modifiers = ['', "'", '2'];
        const scramble = [];

        let lastFace = '';

        for (let i = 0; i < length; i++) {
            let face;
            do {
                face = faces[Math.floor(Math.random() * faces.length)];
            } while (face === lastFace);

            const modifier = modifiers[Math.floor(Math.random() * modifiers.length)];
            scramble.push(face + modifier);
            lastFace = face;
        }

        return scramble.join(' ');
    }

    // Apply scramble
    applyScramble(scrambleString) {
        console.log('Applying scramble:', scrambleString);
        this.applyMoves(scrambleString);
        return this.cubeString;
    }

    // Apply solution
    applySolution(solutionString) {
        console.log('Applying solution:', solutionString);
        this.applyMoves(solutionString);
        return this.cubeString;
    }

    // Check if solved
    isSolved() {
        const faces = this.stringToFaces();
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];

        for (const face of faceOrder) {
            const faceArray = faces[face];
            const centerColor = faceArray[0][0]; // Use first sticker as reference

            for (let row = 0; row < this.size; row++) {
                for (let col = 0; col < this.size; col++) {
                    if (faceArray[row][col] !== centerColor) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    // Get Kociemba format for 3x3 cubes
    getKociembaString() {
        if (this.size !== 3) {
            console.warn('Kociemba format only available for 3x3 cubes');
            return null;
        }

        // Convert WYGBRO to URFDLB format
        const mapping = {
            'W': 'U', 'Y': 'D', 'G': 'F',
            'B': 'B', 'R': 'R', 'O': 'L'
        };

        return this.cubeString.split('').map(c => mapping[c]).join('');
    }

    // Change cube size
    changeSize(newSize) {
        if (![2, 3, 4].includes(newSize)) {
            console.error('Invalid cube size:', newSize);
            return;
        }

        this.size = newSize;
        this.resetToSolved();
        return this.cubeString;
    }

    // Wide moves for 4x4 cubes
    executeWideMove(wideFace, clockwise = true) {
        if (this.size !== 4) {
            console.warn('Wide moves only supported for 4x4 cubes');
            return this.cubeString;
        }

        const baseFace = wideFace.replace('w', '');
        console.log(`Executing wide move: ${wideFace}${clockwise ? '' : "'"}`);

        // Execute outer layer
        this.executeMove(baseFace, clockwise);

        // Execute inner layer (simplified - would need proper slice implementation)
        // For now, just the outer layer

        return this.cubeString;
    }

    // Validation
    validateCubeState() {
        const expectedLength = this.size * this.size * 6;
        if (this.cubeString.length !== expectedLength) {
            return false;
        }

        // Count colors
        const colorCounts = {};
        for (const char of this.cubeString) {
            colorCounts[char] = (colorCounts[char] || 0) + 1;
        }

        const expectedCount = this.size * this.size;
        const validColors = Object.values(this.colorToChar);

        for (const color of validColors) {
            if (colorCounts[color] !== expectedCount) {
                return false;
            }
        }

        return true;
    }
}

// Export for use in other modules
window.CubeState = CubeState;
