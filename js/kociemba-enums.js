/**
 * Kociemba Algorithm Enums
 * JavaScript implementation of the enums from the Python Kociemba solver
 */

// Facelet positions according to Kociemba format
// The cube definition string format: UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB
// Order: U1-U9, R1-R9, F1-F9, D1-D9, L1-L9, B1-B9
const Facelet = {
    // Up face (0-8)
    U1: 0, U2: 1, U3: 2, U4: 3, U5: 4, U6: 5, U7: 6, U8: 7, U9: 8,
    // Right face (9-17)
    R1: 9, R2: 10, R3: 11, R4: 12, R5: 13, R6: 14, R7: 15, R8: 16, R9: 17,
    // Front face (18-26)
    F1: 18, F2: 19, F3: 20, F4: 21, F5: 22, F6: 23, F7: 24, F8: 25, F9: 26,
    // Down face (27-35)
    D1: 27, D2: 28, D3: 29, D4: 30, D5: 31, D6: 32, D7: 33, D8: 34, D9: 35,
    // Left face (36-44)
    L1: 36, L2: 37, L3: 38, L4: 39, L5: 40, L6: 41, L7: 42, L8: 43, L9: 44,
    // Back face (45-53)
    B1: 45, B2: 46, B3: 47, B4: 48, B5: 49, B6: 50, B7: 51, B8: 52, B9: 53
};

// Colors (also used to name faces)
const Color = {
    U: 0, // Up (White)
    R: 1, // Right (Red)
    F: 2, // Front (Green)
    D: 3, // Down (Yellow)
    L: 4, // Left (Orange)
    B: 5  // Back (Blue)
};

// Corner positions
const Corner = {
    URF: 0, // Up-Right-Front
    UFL: 1, // Up-Front-Left
    ULB: 2, // Up-Left-Back
    UBR: 3, // Up-Back-Right
    DFR: 4, // Down-Front-Right
    DLF: 5, // Down-Left-Front
    DBL: 6, // Down-Back-Left
    DRB: 7  // Down-Right-Back
};

// Edge positions
const Edge = {
    UR: 0,  // Up-Right
    UF: 1,  // Up-Front
    UL: 2,  // Up-Left
    UB: 3,  // Up-Back
    DR: 4,  // Down-Right
    DF: 5,  // Down-Front
    DL: 6,  // Down-Left
    DB: 7,  // Down-Back
    FR: 8,  // Front-Right
    FL: 9,  // Front-Left
    BL: 10, // Back-Left
    BR: 11  // Back-Right
};

// Moves in face-turn metric
const Move = {
    U1: 0,  U2: 1,  U3: 2,   // U, U2, U'
    R1: 3,  R2: 4,  R3: 5,   // R, R2, R'
    F1: 6,  F2: 7,  F3: 8,   // F, F2, F'
    D1: 9,  D2: 10, D3: 11,  // D, D2, D'
    L1: 12, L2: 13, L3: 14,  // L, L2, L'
    B1: 15, B2: 16, B3: 17   // B, B2, B'
};

// Move names for display
const MoveNames = [
    'U', 'U2', "U'", 'R', 'R2', "R'", 'F', 'F2', "F'",
    'D', 'D2', "D'", 'L', 'L2', "L'", 'B', 'B2', "B'"
];

// Corner facelet positions
const cornerFacelet = [
    [Facelet.U9, Facelet.R1, Facelet.F3], // URF
    [Facelet.U7, Facelet.F1, Facelet.L3], // UFL
    [Facelet.U1, Facelet.L1, Facelet.B3], // ULB
    [Facelet.U3, Facelet.B1, Facelet.R3], // UBR
    [Facelet.D3, Facelet.F9, Facelet.R7], // DFR
    [Facelet.D1, Facelet.L9, Facelet.F7], // DLF
    [Facelet.D7, Facelet.B9, Facelet.L7], // DBL
    [Facelet.D9, Facelet.R9, Facelet.B7]  // DRB
];

// Edge facelet positions
const edgeFacelet = [
    [Facelet.U6, Facelet.R2], // UR
    [Facelet.U8, Facelet.F2], // UF
    [Facelet.U4, Facelet.L2], // UL
    [Facelet.U2, Facelet.B2], // UB
    [Facelet.D6, Facelet.R8], // DR
    [Facelet.D2, Facelet.F8], // DF
    [Facelet.D4, Facelet.L8], // DL
    [Facelet.D8, Facelet.B8], // DB
    [Facelet.F6, Facelet.R4], // FR
    [Facelet.F4, Facelet.L6], // FL
    [Facelet.B6, Facelet.L4], // BL
    [Facelet.B4, Facelet.R6]  // BR
];

// Corner colors (which colors appear on each corner)
const cornerColor = [
    [Color.U, Color.R, Color.F], // URF
    [Color.U, Color.F, Color.L], // UFL
    [Color.U, Color.L, Color.B], // ULB
    [Color.U, Color.B, Color.R], // UBR
    [Color.D, Color.F, Color.R], // DFR
    [Color.D, Color.L, Color.F], // DLF
    [Color.D, Color.B, Color.L], // DBL
    [Color.D, Color.R, Color.B]  // DRB
];

// Edge colors (which colors appear on each edge)
const edgeColor = [
    [Color.U, Color.R], // UR
    [Color.U, Color.F], // UF
    [Color.U, Color.L], // UL
    [Color.U, Color.B], // UB
    [Color.D, Color.R], // DR
    [Color.D, Color.F], // DF
    [Color.D, Color.L], // DL
    [Color.D, Color.B], // DB
    [Color.F, Color.R], // FR
    [Color.F, Color.L], // FL
    [Color.B, Color.L], // BL
    [Color.B, Color.R]  // BR
];

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.KociembaEnums = {
        Facelet,
        Color,
        Corner,
        Edge,
        Move,
        MoveNames,
        cornerFacelet,
        edgeFacelet,
        cornerColor,
        edgeColor
    };
} else {
    module.exports = {
        Facelet,
        Color,
        Corner,
        Edge,
        Move,
        MoveNames,
        cornerFacelet,
        edgeFacelet,
        cornerColor,
        edgeColor
    };
}
