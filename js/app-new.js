/**
 * Complete Rubik's Cube Application
 * Main application entry point with enhanced features
 */

class App {
    constructor() {
        this.cubeState = null;
        this.cube3D = null;
        this.cube2D = null;
        this.controls = null;
        
        this.init();
    }

    async init() {
        try {
            console.log('🎲 Initializing Complete Rubik\'s Cube Solver...');
            
            // Show loading message
            this.showLoadingMessage();
            
            // Initialize cube state (default 3x3)
            this.cubeState = new CubeState(3);
            console.log('✓ Cube state initialized');
            
            // Initialize 3D visualization
            await this.initialize3D();
            console.log('✓ 3D visualization initialized');
            
            // Initialize 2D visualization
            await this.initialize2D();
            console.log('✓ 2D visualization initialized');
            
            // Initialize controls
            this.controls = new Controls(this.cubeState, this.cube3D, this.cube2D);
            console.log('✓ Controls initialized');
            
            // Check server connection
            await this.checkServerConnection();
            
            // Hide loading message
            this.hideLoadingMessage();
            
            // Show welcome message
            this.showWelcomeMessage();
            
            console.log('🎉 Application initialized successfully!');
            
        } catch (error) {
            console.error('❌ Application initialization failed:', error);
            this.showError('Failed to initialize application: ' + error.message);
            this.hideLoadingMessage();
        }
    }

    async initialize3D() {
        const canvas = document.getElementById('cube-canvas');
        if (!canvas) {
            throw new Error('Canvas element not found');
        }
        
        // Use existing 3D implementation or create a simple placeholder
        if (window.Cube3D) {
            this.cube3D = new Cube3D(canvas, this.cubeState);
        } else {
            // Create a simple placeholder 3D renderer
            this.cube3D = this.createSimple3D(canvas);
        }
    }

    async initialize2D() {
        const flatCube = document.getElementById('flat-cube');
        if (!flatCube) {
            throw new Error('Flat cube container not found');
        }
        
        // Use existing 2D implementation or create a simple one
        if (window.Cube2D) {
            this.cube2D = new Cube2D(flatCube, this.cubeState);
        } else {
            // Create a simple 2D renderer
            this.cube2D = this.createSimple2D(flatCube);
        }
    }

    createSimple3D(canvas) {
        // Simple 3D placeholder
        const ctx = canvas.getContext('2d');
        
        return {
            refresh: () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = '#f0f0f0';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // Draw a simple cube representation
                ctx.fillStyle = '#333';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('3D View', canvas.width / 2, canvas.height / 2 - 20);
                ctx.fillText(`${this.cubeState.size}x${this.cubeState.size} Cube`, canvas.width / 2, canvas.height / 2 + 20);
                
                // Draw cube status
                const status = this.cubeState.isSolved() ? 'SOLVED ✅' : 'SCRAMBLED 🔄';
                ctx.fillStyle = this.cubeState.isSolved() ? '#4caf50' : '#ff9800';
                ctx.fillText(status, canvas.width / 2, canvas.height / 2 + 60);
            },
            rotateLeft: () => console.log('3D: Rotate left'),
            rotateRight: () => console.log('3D: Rotate right'),
            flip: () => console.log('3D: Flip')
        };
    }

    createSimple2D(container) {
        return {
            refresh: () => {
                container.innerHTML = '';
                
                const faces = this.cubeState.stringToFaces();
                const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
                const faceNames = {
                    'U': 'Up', 'R': 'Right', 'F': 'Front',
                    'D': 'Down', 'L': 'Left', 'B': 'Back'
                };
                
                // Create 2D layout
                const layout = document.createElement('div');
                layout.style.cssText = `
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                    grid-template-rows: repeat(3, 1fr);
                    gap: 10px;
                    max-width: 400px;
                    margin: 0 auto;
                `;
                
                // Position faces in cross layout
                const positions = {
                    'U': { gridColumn: '2', gridRow: '1' },
                    'L': { gridColumn: '1', gridRow: '2' },
                    'F': { gridColumn: '2', gridRow: '2' },
                    'R': { gridColumn: '3', gridRow: '2' },
                    'B': { gridColumn: '4', gridRow: '2' },
                    'D': { gridColumn: '2', gridRow: '3' }
                };
                
                faceOrder.forEach(faceName => {
                    const faceDiv = document.createElement('div');
                    faceDiv.style.cssText = `
                        display: grid;
                        grid-template-columns: repeat(${this.cubeState.size}, 1fr);
                        gap: 2px;
                        padding: 5px;
                        background: #333;
                        border-radius: 5px;
                        grid-column: ${positions[faceName].gridColumn};
                        grid-row: ${positions[faceName].gridRow};
                    `;
                    
                    // Add face label
                    const label = document.createElement('div');
                    label.textContent = faceNames[faceName];
                    label.style.cssText = `
                        grid-column: 1 / -1;
                        text-align: center;
                        color: white;
                        font-size: 12px;
                        margin-bottom: 2px;
                    `;
                    faceDiv.appendChild(label);
                    
                    // Add stickers
                    const face = faces[faceName];
                    for (let row = 0; row < this.cubeState.size; row++) {
                        for (let col = 0; col < this.cubeState.size; col++) {
                            const sticker = document.createElement('div');
                            const color = this.cubeState.charToColor[face[row][col]];
                            sticker.className = `cube-sticker ${color}`;
                            sticker.style.cssText = `
                                aspect-ratio: 1;
                                border-radius: 2px;
                                border: 1px solid #333;
                            `;
                            faceDiv.appendChild(sticker);
                        }
                    }
                    
                    layout.appendChild(faceDiv);
                });
                
                container.appendChild(layout);
            }
        };
    }

    async checkServerConnection() {
        try {
            const response = await fetch('http://localhost:5000/health');
            if (response.ok) {
                const data = await response.json();
                console.log('✓ Server connection established:', data.message);
                return true;
            }
        } catch (error) {
            console.warn('⚠️ Server not available:', error.message);
            this.showWarning('Solver server not available. Some features may be limited.');
        }
        return false;
    }

    showLoadingMessage() {
        const loading = document.createElement('div');
        loading.id = 'loading-overlay';
        loading.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
            font-size: 1.5em;
            text-align: center;
        `;
        loading.innerHTML = `
            <div>
                <div style="font-size: 3em; margin-bottom: 20px;">🧩</div>
                <div>Loading Rubik's Cube Solver...</div>
                <div style="font-size: 0.8em; margin-top: 10px; opacity: 0.7;">Please wait while we initialize the application</div>
            </div>
        `;
        document.body.appendChild(loading);
    }

    hideLoadingMessage() {
        const loading = document.getElementById('loading-overlay');
        if (loading) {
            loading.remove();
        }
    }

    showWelcomeMessage() {
        const welcome = document.createElement('div');
        welcome.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            z-index: 1000;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 400px;
        `;
        welcome.innerHTML = `
            <h2 style="margin-bottom: 15px;">🎉 Welcome!</h2>
            <p style="margin-bottom: 20px;">Your Rubik's Cube Solver is ready!</p>
            <p style="font-size: 0.9em; opacity: 0.9; margin-bottom: 20px;">
                • Choose your cube size (2x2, 3x3, or 4x4)<br>
                • Click "Scramble" to mix up the cube<br>
                • Click "Solve" to see the solution<br>
                • Use the learning center to improve your skills
            </p>
            <button onclick="this.parentElement.remove()" style="
                background: white;
                color: #667eea;
                border: none;
                padding: 10px 20px;
                border-radius: 20px;
                cursor: pointer;
                font-weight: 600;
            ">Get Started!</button>
        `;
        document.body.appendChild(welcome);
    }

    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            z-index: 1000;
            max-width: 300px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        `;
        errorDiv.textContent = message;
        
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    showWarning(message) {
        const warningDiv = document.createElement('div');
        warningDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #ff9800, #f57c00);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            z-index: 1000;
            max-width: 300px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        `;
        warningDiv.textContent = message;
        
        document.body.appendChild(warningDiv);
        
        setTimeout(() => {
            if (warningDiv.parentNode) {
                warningDiv.parentNode.removeChild(warningDiv);
            }
        }, 7000);
    }

    // Public API methods
    executeMove(face, clockwise = true) {
        if (this.controls) {
            this.controls.executeFaceMove(face, clockwise);
        }
    }

    scramble() {
        if (this.controls) {
            this.controls.autoScramble();
        }
    }

    solve() {
        if (this.controls) {
            this.controls.solveCube();
        }
    }

    reset() {
        if (this.controls) {
            this.controls.resetCube();
        }
    }

    changeSize(size) {
        if (this.controls) {
            this.controls.changeSize(size);
        }
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});

// Export for global access
window.App = App;
