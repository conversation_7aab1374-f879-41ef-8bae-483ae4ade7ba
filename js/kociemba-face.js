/**
 * Kociemba FaceCube Implementation
 * JavaScript implementation of the FaceCube class from the Python Kociemba solver
 */

class FaceCube {
    constructor() {
        // Initialize with solved cube: 54 facelets
        // Order: U(0-8), R(9-17), F(18-26), D(27-35), <PERSON>(36-44), B(45-53)
        this.f = [];
        
        // Up face (white)
        for (let i = 0; i < 9; i++) {
            this.f.push(KociembaEnums.Color.U);
        }
        // Right face (red)
        for (let i = 0; i < 9; i++) {
            this.f.push(KociembaEnums.Color.R);
        }
        // Front face (green)
        for (let i = 0; i < 9; i++) {
            this.f.push(KociembaEnums.Color.F);
        }
        // Down face (yellow)
        for (let i = 0; i < 9; i++) {
            this.f.push(KociembaEnums.Color.D);
        }
        // Left face (orange)
        for (let i = 0; i < 9; i++) {
            this.f.push(KociembaEnums.Color.L);
        }
        // Back face (blue)
        for (let i = 0; i < 9; i++) {
            this.f.push(KociembaEnums.Color.B);
        }
    }

    toString() {
        return this.toKociembaString();
    }

    fromString(s) {
        /**
         * Construct a facelet cube from a Kociemba format string
         * Format: UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB
         */
        if (s.length < 54) {
            return `Error: Cube definition string ${s} contains less than 54 facelets.`;
        } else if (s.length > 54) {
            return `Error: Cube definition string ${s} contains more than 54 facelets.`;
        }

        const cnt = [0, 0, 0, 0, 0, 0]; // Count for each color
        
        for (let i = 0; i < 54; i++) {
            switch (s[i]) {
                case 'U':
                    this.f[i] = KociembaEnums.Color.U;
                    cnt[KociembaEnums.Color.U]++;
                    break;
                case 'R':
                    this.f[i] = KociembaEnums.Color.R;
                    cnt[KociembaEnums.Color.R]++;
                    break;
                case 'F':
                    this.f[i] = KociembaEnums.Color.F;
                    cnt[KociembaEnums.Color.F]++;
                    break;
                case 'D':
                    this.f[i] = KociembaEnums.Color.D;
                    cnt[KociembaEnums.Color.D]++;
                    break;
                case 'L':
                    this.f[i] = KociembaEnums.Color.L;
                    cnt[KociembaEnums.Color.L]++;
                    break;
                case 'B':
                    this.f[i] = KociembaEnums.Color.B;
                    cnt[KociembaEnums.Color.B]++;
                    break;
                default:
                    return `Error: Invalid character '${s[i]}' in cube definition string.`;
            }
        }

        // Check if we have exactly 9 facelets of each color
        if (cnt.every(x => x === 9)) {
            return true; // Success
        } else {
            return `Error: Cube definition string ${s} does not contain exactly 9 facelets of each color.`;
        }
    }

    toKociembaString() {
        /**
         * Give a Kociemba format string representation of the facelet cube
         */
        let s = '';
        for (let i = 0; i < 54; i++) {
            switch (this.f[i]) {
                case KociembaEnums.Color.U: s += 'U'; break;
                case KociembaEnums.Color.R: s += 'R'; break;
                case KociembaEnums.Color.F: s += 'F'; break;
                case KociembaEnums.Color.D: s += 'D'; break;
                case KociembaEnums.Color.L: s += 'L'; break;
                case KociembaEnums.Color.B: s += 'B'; break;
                default: s += '?'; break;
            }
        }
        return s;
    }

    fromOurCubeState(cubeState) {
        /**
         * Convert from our cube state format to Kociemba format
         * Our format: WYGBRO (white, yellow, green, blue, red, orange)
         * Kociemba format: URFDLB (up, right, front, down, left, back)
         * Our order: U(0-8), R(9-17), F(18-26), D(27-35), L(36-44), B(45-53)
         * Kociemba order: U(0-8), R(9-17), F(18-26), D(27-35), L(36-44), B(45-53)
         */
        const ourToKociemba = {
            'white': 'U',   // White -> Up
            'red': 'R',     // Red -> Right  
            'green': 'F',   // Green -> Front
            'yellow': 'D',  // Yellow -> Down
            'orange': 'L',  // Orange -> Left
            'blue': 'B'     // Blue -> Back
        };

        const faces = cubeState.stringToFaces();
        let kociembaString = '';

        // Convert each face in Kociemba order: U, R, F, D, L, B
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        
        for (const face of faceOrder) {
            const faceArray = faces[face];
            for (let row = 0; row < 3; row++) {
                for (let col = 0; col < 3; col++) {
                    const ourColor = faceArray[row][col];
                    const kociembaColor = ourToKociemba[ourColor];
                    if (kociembaColor) {
                        kociembaString += kociembaColor;
                    } else {
                        console.error(`Unknown color: ${ourColor}`);
                        return null;
                    }
                }
            }
        }

        const result = this.fromString(kociembaString);
        if (result === true) {
            return kociembaString;
        } else {
            console.error('Failed to convert cube state:', result);
            return null;
        }
    }

    to2DString() {
        /**
         * Give a 2D string representation of the facelet cube for debugging
         */
        const s = this.toKociembaString();
        let result = '';
        
        // Up face
        result += '   ' + s.slice(0, 3) + '\n';
        result += '   ' + s.slice(3, 6) + '\n';
        result += '   ' + s.slice(6, 9) + '\n';
        
        // Middle row: L F R B
        for (let row = 0; row < 3; row++) {
            result += s.slice(36 + row * 3, 39 + row * 3) + // L
                     s.slice(18 + row * 3, 21 + row * 3) + // F
                     s.slice(9 + row * 3, 12 + row * 3) +  // R
                     s.slice(45 + row * 3, 48 + row * 3) + // B
                     '\n';
        }
        
        // Down face
        result += '   ' + s.slice(27, 30) + '\n';
        result += '   ' + s.slice(30, 33) + '\n';
        result += '   ' + s.slice(33, 36) + '\n';
        
        return result;
    }

    isSolved() {
        /**
         * Check if the cube is in solved state
         */
        const solvedString = 'UUUUUUUUURRRRRRRRRFFFFFFFFFDDDDDDDDDLLLLLLLLLBBBBBBBBB';
        return this.toKociembaString() === solvedString;
    }

    validate() {
        /**
         * Validate that the cube is in a valid state
         */
        // Check that we have exactly 9 facelets of each color
        const counts = [0, 0, 0, 0, 0, 0];
        for (let i = 0; i < 54; i++) {
            if (this.f[i] >= 0 && this.f[i] < 6) {
                counts[this.f[i]]++;
            } else {
                return `Invalid color value: ${this.f[i]}`;
            }
        }

        for (let i = 0; i < 6; i++) {
            if (counts[i] !== 9) {
                return `Color ${i} appears ${counts[i]} times, expected 9`;
            }
        }

        return true; // Valid
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.FaceCube = FaceCube;
} else {
    module.exports = FaceCube;
}
