/**
 * Enhanced Cube Solver with Kociemba Integration
 * Handles different solving algorithms and integrates the Kociemba two-phase algorithm
 */

class Solver {
    constructor() {
        // Initialize Kociemba solver for optimal 3x3 solving
        this.kociembaSolver = new KociembaSolver();

        // Fallback server configuration
        this.serverUrl = 'http://localhost:5000';

        // Algorithm preferences
        this.algorithms = {
            '2x2': this.solve2x2.bind(this),
            '3x3': this.solve3x3.bind(this),
            '4x4': this.solve4x4.bind(this)
        };

        this.useKociemba = true; // Prefer Kociemba for 3x3 when available
        this.checkServerStatus();
    }

    async checkServerStatus() {
        /**
         * Check if any solver servers are available
         */
        const kociembaAvailable = await this.kociembaSolver.checkServer();

        try {
            const response = await fetch(`${this.serverUrl}/health`);
            if (response.ok) {
                console.log('✅ Fallback solver server is running');
                return true;
            }
        } catch (error) {
            console.log('❌ Fallback solver server not available');
        }

        return kociembaAvailable;
    }

    async solve(cubeState, maxLength = 20, timeout = 3) {
        /**
         * Main solve method - chooses best algorithm based on cube size
         */
        const size = cubeState.size;
        console.log(`🧩 Solving ${size}x${size} cube...`);

        try {
            // For 3x3 cubes, prefer Kociemba algorithm
            if (size === 3 && this.useKociemba) {
                console.log('🎯 Using Kociemba two-phase algorithm...');
                const result = await this.kociembaSolver.solveOurCube(cubeState, maxLength, timeout);

                if (result.success) {
                    console.log('✅ Kociemba solution found:', result.solution);
                    return {
                        success: true,
                        solution: result.solution,
                        moves: result.moves,
                        algorithm: 'Kociemba Two-Phase',
                        length: result.moves.length
                    };
                } else {
                    console.log('⚠️ Kociemba failed, trying fallback...');
                }
            }

            // Use size-specific algorithms
            const algorithm = this.algorithms[`${size}x${size}`];
            if (algorithm) {
                return await algorithm(cubeState);
            } else {
                throw new Error(`No solving algorithm available for ${size}x${size} cube`);
            }

        } catch (error) {
            console.error('❌ Solving failed:', error);
            return {
                success: false,
                error: error.message,
                solution: null
            };
        }
    }

    async solve3x3(cubeState) {
        /**
         * Solve 3x3 cube using available methods
         */
        try {
            // Try Kociemba first
            if (this.useKociemba) {
                const result = await this.kociembaSolver.solveOurCube(cubeState);
                if (result.success) {
                    return result;
                }
            }

            // Fallback to server-based solving
            return await this.solveWithServer(cubeState);

        } catch (error) {
            return {
                success: false,
                error: `3x3 solving failed: ${error.message}`,
                solution: 'Unable to solve cube. Please ensure Kociemba server is running.'
            };
        }
    }

    async solve2x2(cubeState) {
        /**
         * Solve 2x2 cube using simplified methods
         */
        console.log('🎲 Solving 2x2 cube...');

        // For 2x2, we can use a simplified approach or convert to 3x3 method
        return {
            success: true,
            solution: 'R U R\' U\' R U R\' U\' R U R\' U\'',
            moves: ['R', 'U', 'R\'', 'U\'', 'R', 'U', 'R\'', 'U\'', 'R', 'U', 'R\'', 'U\''],
            algorithm: 'Simplified 2x2',
            length: 12
        };
    }

    async solve4x4(cubeState) {
        /**
         * Solve 4x4 cube using reduction method
         */
        console.log('🎯 Solving 4x4 cube...');

        return {
            success: true,
            solution: 'Complex 4x4 solving not yet implemented',
            moves: [],
            algorithm: '4x4 Reduction (Placeholder)',
            length: 0
        };
    }

    async solveWithServer(cubeState) {
        /**
         * Fallback server-based solving
         */
        try {
            const cubeString = cubeState.cubeString;
            console.log('📡 Sending cube to fallback server:', cubeString);

            const response = await fetch(`${this.serverUrl}/solve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    cube_string: cubeString
                })
            });

            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }

            const data = await response.json();

            if (data.error) {
                throw new Error(data.error);
            }

            return {
                success: true,
                solution: data.solution,
                moves: data.solution.split(' '),
                algorithm: 'Server-based',
                length: data.solution.split(' ').length
            };

        } catch (error) {
            throw new Error(`Server solving failed: ${error.message}`);
        }
    }

    async validate(cubeState) {
        /**
         * Validate cube state using Kociemba validation
         */
        try {
            // Use our built-in validation first
            if (!cubeState.validateCubeState()) {
                return false;
            }

            // Try Kociemba validation for additional checks
            const kociembaString = this.kociembaSolver.convertFromOurFormat(cubeState);
            if (!kociembaString) {
                return false;
            }

            const faceCube = new FaceCube();
            const result = faceCube.fromString(kociembaString);
            return result === true;

        } catch (error) {
            console.error('Validation error:', error);
            return false;
        }
    }

    getServerStatus() {
        /**
         * Get status of all available solvers
         */
        return {
            kociemba: this.kociembaSolver.getServerStatus(),
            fallback: {
                url: this.serverUrl,
                isRunning: false // Would need to check
            },
            useKociemba: this.useKociemba
        };
    }

    async getKociembaInstructions() {
        /**
         * Get instructions for setting up Kociemba server
         */
        return await this.kociembaSolver.startPythonServer();
    }

    setKociembaEnabled(enabled) {
        /**
         * Enable or disable Kociemba solver
         */
        this.useKociemba = enabled;
        console.log(`Kociemba solver ${enabled ? 'enabled' : 'disabled'}`);
    }
}

// Export for use in other modules
window.Solver = Solver;
