/**
 * Complete Cube Controls System
 * Handles all user interactions and cube operations
 */

class Controls {
    constructor(cubeState, cube3D, cube2D) {
        this.cubeState = cubeState;
        this.cube3D = cube3D;
        this.cube2D = cube2D;
        
        this.moveCount = 0;
        this.startTime = null;
        this.currentScramble = '';
        
        this.initializeEventListeners();
        this.updateDisplay();
    }

    initializeEventListeners() {
        // Size selector
        document.querySelectorAll('.size-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const size = parseInt(e.target.dataset.size);
                this.changeSize(size);
            });
        });

        // Face controls
        document.querySelectorAll('.face-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const face = e.target.dataset.face;
                const clockwise = e.target.dataset.clockwise === 'true';
                this.executeFaceMove(face, clockwise);
            });
        });

        // Wide move controls
        document.querySelectorAll('.wide-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const face = e.target.dataset.face;
                const clockwise = e.target.dataset.clockwise === 'true';
                this.executeWideMove(face, clockwise);
            });
        });

        // Action buttons
        document.getElementById('auto-scramble')?.addEventListener('click', () => this.autoScramble());
        document.getElementById('solve-cube')?.addEventListener('click', () => this.solveCube());
        document.getElementById('reset-cube')?.addEventListener('click', () => this.resetCube());
        document.getElementById('step-solve')?.addEventListener('click', () => this.stepSolve());

        // Manual input
        document.getElementById('apply-manual-moves')?.addEventListener('click', () => this.applyManualMoves());
        document.getElementById('manual-moves-input')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.applyManualMoves();
        });

        // Orientation controls
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                if (action) this.handleOrientation(action);
            });
        });

        // Learning tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.target.dataset.tab;
                this.switchTab(tab);
            });
        });

        // Try algorithm buttons
        document.querySelectorAll('.try-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const moves = e.target.dataset.moves;
                this.tryAlgorithm(moves);
            });
        });
    }

    executeFaceMove(face, clockwise) {
        console.log(`Executing face move: ${face}${clockwise ? '' : "'"}`);

        // Handle double moves
        if (face.includes('2')) {
            const baseFace = face.replace('2', '');
            this.cubeState.executeMove(baseFace, true);
            this.cubeState.executeMove(baseFace, true);
        } else {
            this.cubeState.executeMove(face, clockwise);
        }

        this.moveCount++;
        this.updateVisualizations();
        this.updateDisplay();
        this.checkIfSolved();
    }

    executeWideMove(face, clockwise) {
        console.log(`Executing wide move: ${face}${clockwise ? '' : "'"}`);
        
        this.cubeState.executeWideMove(face, clockwise);
        this.moveCount++;
        this.updateVisualizations();
        this.updateDisplay();
        this.checkIfSolved();
    }

    async autoScramble() {
        console.log('Auto scramble requested');
        
        try {
            // Generate scramble
            const scramble = this.cubeState.generateScramble(20);
            this.currentScramble = scramble;
            
            // Update display
            document.getElementById('current-scramble').textContent = scramble;
            
            // Apply scramble with animation
            await this.applyMovesWithAnimation(scramble);
            
            // Reset counters
            this.moveCount = 0;
            this.startTime = Date.now();
            this.updateDisplay();
            
        } catch (error) {
            console.error('Scramble error:', error);
            this.showError('Failed to scramble cube: ' + error.message);
        }
    }

    async solveCube() {
        console.log('Solve cube requested');
        
        if (this.cubeState.isSolved()) {
            this.showMessage('Cube is already solved! 🎉', 'success');
            return;
        }

        try {
            // Show loading
            const solutionDisplay = document.getElementById('solution-moves');
            if (solutionDisplay) {
                solutionDisplay.innerHTML = '<div class="loading">🔄 Solving cube...</div>';
            }

            // Get solution from server
            const solution = await this.getSolution();
            
            if (solution && solution.trim()) {
                console.log('Solution found:', solution);
                
                // Apply solution with animation
                await this.applyMovesWithAnimation(solution);
                
                // Update display
                if (solutionDisplay) {
                    solutionDisplay.innerHTML = `<strong>Solution Applied:</strong><br>${solution}`;
                }
                
                if (this.cubeState.isSolved()) {
                    this.showSolvedMessage();
                }
            } else {
                if (solutionDisplay) {
                    solutionDisplay.innerHTML = '<strong>Info:</strong> No moves needed or solver returned empty solution.';
                }
            }
            
        } catch (error) {
            console.error('Solve error:', error);
            const solutionDisplay = document.getElementById('solution-moves');
            if (solutionDisplay) {
                solutionDisplay.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
            this.showError('Failed to solve cube: ' + error.message);
        }
    }

    async getSolution() {
        let cubeString;
        
        // For 3x3 cubes, use Kociemba format
        if (this.cubeState.size === 3) {
            cubeString = this.cubeState.getKociembaString();
            if (!cubeString) {
                throw new Error('Cannot convert cube to Kociemba format');
            }
        } else {
            // For 2x2 and 4x4, use the internal cube string format
            cubeString = this.cubeState.cubeString;
        }

        const response = await fetch('http://localhost:5000/solve', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ cube_string: cubeString })
        });

        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        return data.solution || '';
    }

    resetCube() {
        console.log('Reset cube requested');
        
        this.cubeState.resetToSolved();
        this.moveCount = 0;
        this.startTime = null;
        this.currentScramble = '';
        
        // Clear displays
        document.getElementById('current-scramble').textContent = 'None';
        document.getElementById('solution-moves').textContent = 'Ready to solve';
        
        this.updateVisualizations();
        this.updateDisplay();
        
        this.showMessage('Cube reset to solved state', 'info');
    }

    async stepSolve() {
        // Placeholder for step-by-step solving
        this.showMessage('Step-by-step solving coming soon!', 'info');
    }

    applyManualMoves() {
        const input = document.getElementById('manual-moves-input');
        const moves = input.value.trim();
        
        if (!moves) return;
        
        try {
            this.cubeState.applyMoves(moves);
            this.moveCount += moves.split(' ').length;
            
            this.updateVisualizations();
            this.updateDisplay();
            this.checkIfSolved();
            
            input.value = '';
            this.showMessage('Moves applied successfully', 'success');
            
        } catch (error) {
            console.error('Manual moves error:', error);
            this.showError('Failed to apply moves: ' + error.message);
        }
    }

    async applyMovesWithAnimation(moveSequence, delay = 300) {
        const moves = moveSequence.trim().split(/\s+/).filter(move => move);
        
        for (const move of moves) {
            const cleanMove = move.trim();
            if (!cleanMove) continue;
            
            let face = cleanMove[0];
            let clockwise = true;
            
            if (cleanMove.includes("'") || cleanMove.includes("'")) {
                clockwise = false;
            } else if (cleanMove.includes('2')) {
                // Double move
                this.cubeState.executeMove(face, true);
                this.updateVisualizations();
                await this.sleep(delay / 2);
                this.cubeState.executeMove(face, true);
                this.updateVisualizations();
                await this.sleep(delay / 2);
                continue;
            }
            
            this.cubeState.executeMove(face, clockwise);
            this.updateVisualizations();
            await this.sleep(delay);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    changeSize(newSize) {
        console.log(`Changing cube size to ${newSize}x${newSize}`);
        
        // Update active button
        document.querySelectorAll('.size-btn').forEach(btn => {
            btn.classList.toggle('active', parseInt(btn.dataset.size) === newSize);
        });
        
        // Show/hide wide controls for 4x4
        const wideControls = document.getElementById('wide-controls');
        if (wideControls) {
            wideControls.style.display = newSize === 4 ? 'block' : 'none';
        }
        
        // Change cube state
        this.cubeState.changeSize(newSize);
        this.moveCount = 0;
        this.startTime = null;
        this.currentScramble = '';
        
        this.updateVisualizations();
        this.updateDisplay();
        
        this.showMessage(`Switched to ${newSize}x${newSize} cube`, 'info');
    }

    handleOrientation(action) {
        console.log(`Orientation action: ${action}`);

        switch (action) {
            case 'left':
                this.cube3D?.rotateLeft();
                break;
            case 'right':
                this.cube3D?.rotateRight();
                break;
            case 'flip':
                this.cube3D?.flip();
                break;
        }

        console.log('Visual orientation changed, cube state unchanged');
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}-tab`);
        });
    }

    tryAlgorithm(moves) {
        console.log(`Trying algorithm: ${moves}`);

        try {
            this.cubeState.applyMoves(moves);
            this.moveCount += moves.split(' ').length;

            this.updateVisualizations();
            this.updateDisplay();
            this.checkIfSolved();

            this.showMessage(`Algorithm applied: ${moves}`, 'success');

        } catch (error) {
            console.error('Algorithm error:', error);
            this.showError('Failed to apply algorithm: ' + error.message);
        }
    }

    updateVisualizations() {
        if (this.cube3D) {
            this.cube3D.refresh();
        }
        if (this.cube2D) {
            this.cube2D.refresh();
        }
    }

    updateDisplay() {
        // Update move count
        const moveCountElement = document.getElementById('move-count');
        if (moveCountElement) {
            moveCountElement.textContent = this.moveCount;
        }

        // Update solve time
        const solveTimeElement = document.getElementById('solve-time');
        if (solveTimeElement && this.startTime) {
            const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            solveTimeElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // Update cube status
        const cubeStatusElement = document.getElementById('cube-status');
        if (cubeStatusElement) {
            cubeStatusElement.textContent = this.cubeState.isSolved() ? 'Solved ✅' : 'Scrambled 🔄';
        }
    }

    checkIfSolved() {
        if (this.cubeState.isSolved()) {
            this.showSolvedMessage();
        }
    }

    showSolvedMessage() {
        const endTime = Date.now();
        const solveTime = this.startTime ? Math.floor((endTime - this.startTime) / 1000) : 0;

        this.showMessage(`🎉 Cube solved in ${this.moveCount} moves and ${solveTime} seconds!`, 'success');

        // Reset timer
        this.startTime = null;
    }

    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.textContent = message;

        // Style the message
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            max-width: 300px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            animation: slideIn 0.3s ease;
        `;

        // Set background color based on type
        switch (type) {
            case 'success':
                messageDiv.style.background = 'linear-gradient(45deg, #4ecdc4, #44a08d)';
                break;
            case 'error':
                messageDiv.style.background = 'linear-gradient(45deg, #ff6b6b, #ee5a52)';
                break;
            case 'info':
            default:
                messageDiv.style.background = 'linear-gradient(45deg, #45b7d1, #3498db)';
                break;
        }

        document.body.appendChild(messageDiv);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    messageDiv.parentNode.removeChild(messageDiv);
                }, 300);
            }
        }, 3000);
    }

    showError(message) {
        this.showMessage(message, 'error');
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .loading {
        display: inline-block;
        animation: pulse 1.5s ease-in-out infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
`;
document.head.appendChild(style);

// Export for use in other modules
window.Controls = Controls;
