<!DOCTYPE html>
<html>
<head>
    <title>Test Simple Scramble</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🧪 Test Simple Scramble</h1>
    
    <button onclick="testSimpleScramble()">Test R2 Scramble & Solve</button>
    <button onclick="testKnownScramble()">Test Known Valid Scramble</button>
    <div id="results"></div>

    <script src="js/cube-state.js"></script>
    <script>
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testSimpleScramble() {
            clearResults();
            log('🧪 Testing simple R2 scramble and solve...', 'info');

            try {
                const cube = new CubeState(3);
                
                // Use R2 - this should definitely be solvable
                const scramble = 'R2';
                log(`Using scramble: ${scramble}`, 'info');
                
                cube.applyScramble(scramble);
                log('✅ Scramble applied', 'success');
                
                // Check if cube changed
                if (cube.isSolved()) {
                    log('⚠️ Cube is still solved after R2 - this is wrong!', 'error');
                    return;
                }
                
                log('✅ Cube is properly scrambled', 'success');
                
                // Convert to Kociemba
                const kociembaString = cube.getKociembaString();
                if (!kociembaString) {
                    log('❌ Failed to convert to Kociemba format', 'error');
                    return;
                }
                
                log(`✅ Kociemba: ${kociembaString}`, 'success');
                
                // Send to solver
                const response = await fetch('http://localhost:5000/solve', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cube_string: kociembaString })
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(`❌ Server error: ${errorText}`, 'error');
                    return;
                }
                
                const data = await response.json();
                
                if (data.error) {
                    log(`❌ Solver error: ${data.error}`, 'error');
                    return;
                }
                
                log(`✅ Solution: ${data.solution}`, 'success');
                
                // Apply solution
                cube.applySolution(data.solution);
                
                if (cube.isSolved()) {
                    log('🎉 SUCCESS! Cube solved with R2 scramble!', 'success');
                } else {
                    log('❌ Solution did not solve the cube', 'error');
                }
                
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function testKnownScramble() {
            clearResults();
            log('🧪 Testing known valid scramble...', 'info');

            try {
                const cube = new CubeState(3);
                
                // Use a known valid scramble from WCA
                const scramble = 'D2 F2 U2 R2 D2 F2 R2 U2 L2 F2';
                log(`Using scramble: ${scramble}`, 'info');
                
                cube.applyScramble(scramble);
                log('✅ Scramble applied', 'success');
                
                // This should definitely not be solved
                if (cube.isSolved()) {
                    log('❌ Cube is still solved after complex scramble!', 'error');
                    return;
                }
                
                log('✅ Cube is properly scrambled', 'success');
                
                // Show cube state
                log(`Cube state: ${cube.cubeString.substring(0, 30)}...`, 'info');
                
                // Convert to Kociemba
                const kociembaString = cube.getKociembaString();
                if (!kociembaString) {
                    log('❌ Failed to convert to Kociemba format', 'error');
                    
                    // Debug the cube state
                    const faces = cube.stringToFaces();
                    log('Centers after scramble:', 'info');
                    log(`U: ${faces.U[1][1]}, R: ${faces.R[1][1]}, F: ${faces.F[1][1]}, D: ${faces.D[1][1]}, L: ${faces.L[1][1]}, B: ${faces.B[1][1]}`, 'info');
                    
                    return;
                }
                
                log(`✅ Kociemba: ${kociembaString.substring(0, 30)}...`, 'success');
                
                // Send to solver
                const response = await fetch('http://localhost:5000/solve', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cube_string: kociembaString })
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(`❌ Server error: ${errorText}`, 'error');
                    return;
                }
                
                const data = await response.json();
                
                if (data.error) {
                    log(`❌ Solver error: ${data.error}`, 'error');
                    return;
                }
                
                log(`✅ Solution found: ${data.solution}`, 'success');
                log(`Move count: ${data.move_count}`, 'info');
                
                // Apply solution
                cube.applySolution(data.solution);
                
                if (cube.isSolved()) {
                    log('🎉 SUCCESS! Complex scramble solved!', 'success');
                } else {
                    log('❌ Solution did not solve the cube', 'error');
                }
                
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
