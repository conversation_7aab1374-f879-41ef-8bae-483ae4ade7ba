<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cube Rotation Tests</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #4CAF50;
            margin-bottom: 30px;
        }
        .test-output {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 500px;
            overflow-y: auto;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #1976d2;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196f3; }
        
        .stats {
            background: #2a2a2a;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        
        .stats h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.3s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Cube Rotation Test Suite</h1>
        
        <div class="controls">
            <button onclick="runTests()" id="runBtn">Run All Tests</button>
            <button onclick="clearOutput()" id="clearBtn">Clear Output</button>
            <button onclick="testSingleMove()" id="singleBtn">Test Single Move</button>
        </div>
        
        <div class="stats" id="stats" style="display: none;">
            <h3>Test Progress</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="statsText">Preparing tests...</div>
        </div>
        
        <div class="test-output" id="output">
            Click "Run All Tests" to start the comprehensive cube rotation test suite.
            
            This will test:
            • Basic face rotations (U, D, F, B, R, L)
            • Edge cycle correctness
            • Complex move sequences
            • Multiple move handling
            • Cube state validation
            • Center preservation
        </div>
    </div>

    <script src="js/cube-state.js"></script>
    <script src="cube-rotation-test.js"></script>
    <script>
        let testRunner = null;
        let isRunning = false;

        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const span = document.createElement('span');
            span.className = type;
            span.textContent = message + '\n';
            output.appendChild(span);
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = '';
            document.getElementById('stats').style.display = 'none';
        }

        async function runTests() {
            if (isRunning) return;
            
            isRunning = true;
            const runBtn = document.getElementById('runBtn');
            const statsDiv = document.getElementById('stats');
            const progressFill = document.getElementById('progressFill');
            const statsText = document.getElementById('statsText');
            
            runBtn.disabled = true;
            runBtn.textContent = 'Running Tests...';
            statsDiv.style.display = 'block';
            
            clearOutput();
            
            try {
                log('🚀 Starting Cube Rotation Test Suite...', 'info');
                log('=====================================', 'info');
                
                testRunner = new CubeRotationTest();
                
                // Override the addResult method to update UI
                const originalAddResult = testRunner.addResult.bind(testRunner);
                let testCount = 0;
                const estimatedTotalTests = 50; // Rough estimate
                
                testRunner.addResult = function(message, success) {
                    originalAddResult(message, success);
                    testCount++;
                    
                    const progress = Math.min((testCount / estimatedTotalTests) * 100, 100);
                    progressFill.style.width = progress + '%';
                    statsText.textContent = `Completed ${testCount} tests...`;
                    
                    const type = success ? 'success' : 'error';
                    log(message, type);
                };
                
                // Run tests with small delays for UI updates
                await new Promise(resolve => {
                    setTimeout(() => {
                        const results = testRunner.runAllTests();
                        
                        // Final statistics
                        const passed = results.filter(r => r.success).length;
                        const total = results.length;
                        const percentage = ((passed / total) * 100).toFixed(1);
                        
                        progressFill.style.width = '100%';
                        statsText.innerHTML = `
                            <strong>Final Results:</strong><br>
                            Passed: ${passed}/${total} tests (${percentage}%)
                        `;
                        
                        log('\n🏁 Test Suite Complete!', 'info');
                        log(`📊 Final Score: ${passed}/${total} tests passed (${percentage}%)`, 'info');
                        
                        if (passed === total) {
                            log('🎉 All tests passed! Cube rotations are working perfectly!', 'success');
                        } else {
                            log('⚠️  Some tests failed. Please review the results above.', 'warning');
                        }
                        
                        resolve();
                    }, 100);
                });
                
            } catch (error) {
                log(`❌ Test suite failed with error: ${error.message}`, 'error');
                console.error('Test error:', error);
            } finally {
                isRunning = false;
                runBtn.disabled = false;
                runBtn.textContent = 'Run All Tests';
            }
        }

        function testSingleMove() {
            if (isRunning) return;
            
            clearOutput();
            log('🔍 Testing Single Move...', 'info');
            
            try {
                const cube = new CubeState(3);
                cube.resetToSolved();
                
                log('Initial state: ' + cube.cubeString, 'info');
                
                // Test R move
                cube.executeMove('R', true);
                log('After R move: ' + cube.cubeString, 'info');
                
                // Test R' move
                cube.executeMove('R', false);
                log('After R\' move: ' + cube.cubeString, 'info');
                
                if (cube.isSolved()) {
                    log('✅ Single move test passed - cube returned to solved state', 'success');
                } else {
                    log('❌ Single move test failed - cube not solved after R R\'', 'error');
                }
                
                // Validate cube state
                if (cube.validateCubeState()) {
                    log('✅ Cube state validation passed', 'success');
                } else {
                    log('❌ Cube state validation failed', 'error');
                }
                
            } catch (error) {
                log(`❌ Single move test failed: ${error.message}`, 'error');
            }
        }

        // Initialize
        log('🎲 Cube Rotation Test Suite Ready', 'info');
        log('Click "Run All Tests" to begin comprehensive testing.', 'info');
    </script>
</body>
</html>
