<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Rubik's Cube Solver - Learn & Practice</title>
    <link rel="stylesheet" href="styles-new.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🧩 Complete Rubik's Cube Solver</h1>
            <p class="subtitle">Learn, Practice, and Master Rubik's Cubes</p>
            <div class="cube-size-selector">
                <button class="size-btn" data-size="2">2x2 Pocket Cube</button>
                <button class="size-btn active" data-size="3">3x3 Classic Cube</button>
                <button class="size-btn" data-size="4">4x4 Revenge Cube</button>
            </div>
        </header>

        <main>
            <!-- Cube Visualization -->
            <div class="cube-container">
                <div class="view-section">
                    <h3>3D Interactive View</h3>
                    <canvas id="cube-canvas" width="400" height="400"></canvas>
                    
                    <div class="orientation-controls">
                        <button class="control-btn" data-action="left">← Rotate Left</button>
                        <button class="control-btn" data-action="right">Rotate Right →</button>
                        <button class="control-btn" data-action="flip">↕ Flip</button>
                        <button class="control-btn" id="toggle-auto-rotate">🔄 Auto Rotate</button>
                    </div>
                </div>

                <div class="view-section">
                    <h3>2D Flat View</h3>
                    <div class="flat-cube" id="flat-cube"></div>
                    <div class="cube-info">
                        <div class="cube-state-info">
                            <strong>Current State:</strong> <span id="cube-status">Solved</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Learning Section -->
            <div class="learning-section">
                <h3>📚 Learning Center</h3>
                <div class="learning-tabs">
                    <button class="tab-btn active" data-tab="notation">Notation</button>
                    <button class="tab-btn" data-tab="algorithms">Algorithms</button>
                    <button class="tab-btn" data-tab="methods">Methods</button>
                </div>
                
                <div class="tab-content active" id="notation-tab">
                    <h4>Cube Notation Guide</h4>
                    <div class="notation-grid">
                        <div class="notation-item">
                            <span class="move">R</span>
                            <span class="description">Right face clockwise</span>
                        </div>
                        <div class="notation-item">
                            <span class="move">R'</span>
                            <span class="description">Right face counterclockwise</span>
                        </div>
                        <div class="notation-item">
                            <span class="move">R2</span>
                            <span class="description">Right face 180°</span>
                        </div>
                        <div class="notation-item">
                            <span class="move">U</span>
                            <span class="description">Up face clockwise</span>
                        </div>
                        <div class="notation-item">
                            <span class="move">F</span>
                            <span class="description">Front face clockwise</span>
                        </div>
                        <div class="notation-item">
                            <span class="move">L</span>
                            <span class="description">Left face clockwise</span>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="algorithms-tab">
                    <h4>Common Algorithms</h4>
                    <div class="algorithm-list">
                        <div class="algorithm-item">
                            <strong>Sexy Move:</strong> R U R' U'
                            <button class="try-btn" data-moves="R U R' U'">Try It</button>
                        </div>
                        <div class="algorithm-item">
                            <strong>T-Perm:</strong> R U R' F' R U R' U' R' F R2 U' R'
                            <button class="try-btn" data-moves="R U R' F' R U R' U' R' F R2 U' R'">Try It</button>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="methods-tab">
                    <h4>Solving Methods</h4>
                    <div class="method-list">
                        <div class="method-item">
                            <h5>2x2 - Ortega Method</h5>
                            <p>1. Solve one face<br>2. Orient last layer<br>3. Permute last layer</p>
                        </div>
                        <div class="method-item">
                            <h5>3x3 - CFOP Method</h5>
                            <p>1. Cross<br>2. F2L (First Two Layers)<br>3. OLL (Orient Last Layer)<br>4. PLL (Permute Last Layer)</p>
                        </div>
                        <div class="method-item">
                            <h5>4x4 - Reduction Method</h5>
                            <p>1. Solve centers<br>2. Pair edges<br>3. Solve as 3x3</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Controls Section -->
            <div class="controls-section">
                <h3>🎮 Cube Controls</h3>
                
                <!-- Face Controls -->
                <div class="face-controls">
                    <h4>Face Rotations</h4>
                    <div class="face-grid">
                        <div class="face-row">
                            <button class="face-btn" data-face="U" data-clockwise="true">U</button>
                            <button class="face-btn" data-face="U" data-clockwise="false">U'</button>
                            <button class="face-btn" data-face="U2" data-clockwise="true">U2</button>
                        </div>
                        <div class="face-row">
                            <button class="face-btn" data-face="L" data-clockwise="true">L</button>
                            <button class="face-btn" data-face="F" data-clockwise="true">F</button>
                            <button class="face-btn" data-face="R" data-clockwise="true">R</button>
                            <button class="face-btn" data-face="B" data-clockwise="true">B</button>
                        </div>
                        <div class="face-row">
                            <button class="face-btn" data-face="L" data-clockwise="false">L'</button>
                            <button class="face-btn" data-face="F" data-clockwise="false">F'</button>
                            <button class="face-btn" data-face="R" data-clockwise="false">R'</button>
                            <button class="face-btn" data-face="B" data-clockwise="false">B'</button>
                        </div>
                        <div class="face-row">
                            <button class="face-btn" data-face="D" data-clockwise="true">D</button>
                            <button class="face-btn" data-face="D" data-clockwise="false">D'</button>
                            <button class="face-btn" data-face="D2" data-clockwise="true">D2</button>
                        </div>
                    </div>
                </div>

                <!-- Wide Move Controls (4x4) -->
                <div class="wide-controls" id="wide-controls" style="display: none;">
                    <h4>Wide Moves (4x4)</h4>
                    <div class="wide-grid">
                        <button class="wide-btn" data-face="Uw" data-clockwise="true">Uw</button>
                        <button class="wide-btn" data-face="Uw" data-clockwise="false">Uw'</button>
                        <button class="wide-btn" data-face="Rw" data-clockwise="true">Rw</button>
                        <button class="wide-btn" data-face="Rw" data-clockwise="false">Rw'</button>
                    </div>
                </div>
            </div>

            <!-- Action Controls -->
            <div class="action-controls">
                <h3>🎲 Actions</h3>
                <div class="action-buttons">
                    <button id="auto-scramble" class="action-btn primary">🎲 Scramble</button>
                    <button id="solve-cube" class="action-btn success">🧩 Solve</button>
                    <button id="reset-cube" class="action-btn secondary">🔄 Reset</button>
                    <button id="step-solve" class="action-btn info">👣 Step-by-Step</button>
                </div>
            </div>

            <!-- Manual Input -->
            <div class="manual-input">
                <h3>✏️ Manual Input</h3>
                <div class="input-group">
                    <input type="text" id="manual-moves-input" placeholder="Enter moves (e.g., R U R' U' R' F R2 U' R' U' R U R' F')" />
                    <button id="apply-manual-moves" class="action-btn">Apply Moves</button>
                </div>
                <div class="input-help">
                    <small>Use standard notation: R, L, U, D, F, B (add ' for counter-clockwise, 2 for double turns)</small>
                </div>
            </div>

            <!-- Status Display -->
            <div class="status-section">
                <div class="status-grid">
                    <div class="status-item">
                        <h4>🎯 Current Scramble</h4>
                        <div id="current-scramble" class="status-content">None</div>
                    </div>
                    <div class="status-item">
                        <h4>🔧 Solution</h4>
                        <div id="solution-moves" class="status-content">Ready to solve</div>
                    </div>
                    <div class="status-item">
                        <h4>📊 Statistics</h4>
                        <div id="cube-stats" class="status-content">
                            <div>Moves: <span id="move-count">0</span></div>
                            <div>Time: <span id="solve-time">--:--</span></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Color Reference -->
            <div class="color-reference">
                <h4>🎨 Color Reference</h4>
                <div class="color-mappings">
                    <div class="color-item">
                        <span class="color-box white"></span>
                        <span class="color-label">U (Up) - White</span>
                    </div>
                    <div class="color-item">
                        <span class="color-box yellow"></span>
                        <span class="color-label">D (Down) - Yellow</span>
                    </div>
                    <div class="color-item">
                        <span class="color-box green"></span>
                        <span class="color-label">F (Front) - Green</span>
                    </div>
                    <div class="color-item">
                        <span class="color-box blue"></span>
                        <span class="color-label">B (Back) - Blue</span>
                    </div>
                    <div class="color-item">
                        <span class="color-box red"></span>
                        <span class="color-label">R (Right) - Red</span>
                    </div>
                    <div class="color-item">
                        <span class="color-box orange"></span>
                        <span class="color-label">L (Left) - Orange</span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="js/cube-state-new.js"></script>
    <script src="js/cube-3d.js"></script>
    <script src="js/cube-2d.js"></script>
    <script src="js/controls-new.js"></script>
    <script src="js/app-new.js"></script>
</body>
</html>
