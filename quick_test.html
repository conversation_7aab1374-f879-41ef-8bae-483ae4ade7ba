<!DOCTYPE html>
<html>
<head>
    <title>Quick Cube Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>Quick Cube Test</h1>
    <button onclick="testBasicMoves()">Test Basic Moves</button>
    <button onclick="testScrambleAndSolve()">Test Scramble & Solve</button>
    <div id="results"></div>

    <script src="js/cube-state.js"></script>
    <script>
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }

        function testBasicMoves() {
            document.getElementById('results').innerHTML = '';
            log('Testing basic moves...', 'info');

            try {
                const cube = new CubeState(3);
                const initial = cube.cubeString;
                
                log(`Initial: ${initial.substring(0, 20)}...`, 'info');

                // Test R R'
                cube.executeMove('R', true);
                const afterR = cube.cubeString;
                log(`After R: ${afterR.substring(0, 20)}...`, 'info');

                cube.executeMove('R', false);
                const afterRPrime = cube.cubeString;
                log(`After R': ${afterRPrime.substring(0, 20)}...`, 'info');

                if (afterRPrime === initial) {
                    log('✓ R R\' sequence works correctly!', 'success');
                } else {
                    log('✗ R R\' sequence failed', 'error');
                    return;
                }

                // Test R4
                cube.executeMove('R', true);
                cube.executeMove('R', true);
                cube.executeMove('R', true);
                cube.executeMove('R', true);

                if (cube.cubeString === initial) {
                    log('✓ R4 sequence works correctly!', 'success');
                } else {
                    log('✗ R4 sequence failed', 'error');
                    return;
                }

                // Test all moves
                const moves = ['R', 'L', 'U', 'D', 'F', 'B'];
                let allPassed = true;

                for (const move of moves) {
                    cube.resetToSolved();
                    const before = cube.cubeString;
                    
                    cube.executeMove(move, true);
                    cube.executeMove(move, false);
                    
                    if (cube.cubeString === before) {
                        log(`✓ ${move} ${move}' works correctly`, 'success');
                    } else {
                        log(`✗ ${move} ${move}' failed`, 'error');
                        allPassed = false;
                    }
                }

                if (allPassed) {
                    log('🎉 All basic moves work correctly!', 'success');
                } else {
                    log('❌ Some moves failed', 'error');
                }

            } catch (error) {
                log(`Error: ${error.message}`, 'error');
            }
        }

        async function testScrambleAndSolve() {
            log('Testing scramble and solve...', 'info');

            try {
                const cube = new CubeState(3);
                const initialState = cube.cubeString;
                log(`Initial state: ${initialState.substring(0, 20)}...`, 'info');

                // Generate a simple scramble first
                const scramble = cube.generateScramble(5); // Start with fewer moves
                log(`Scramble: ${scramble}`, 'info');

                // Apply scramble step by step
                log('Applying scramble step by step...', 'info');
                cube.applyScramble(scramble);

                const scrambledState = cube.cubeString;
                log(`Scrambled state: ${scrambledState.substring(0, 20)}...`, 'info');

                if (cube.isSolved()) {
                    log('⚠️ Cube is still solved after scramble - trying different scramble', 'error');

                    // Try a specific scramble that should definitely change the cube
                    const specificScramble = "R U R' U'";
                    log(`Trying specific scramble: ${specificScramble}`, 'info');
                    cube.resetToSolved();
                    cube.applyScramble(specificScramble);

                    if (cube.isSolved()) {
                        log('❌ Cube is still solved even after specific scramble', 'error');
                        return;
                    }
                }

                log('Cube successfully scrambled', 'success');

                // Get Kociemba string
                log('Converting to Kociemba format...', 'info');
                const kociembaString = cube.getKociembaString();
                if (!kociembaString) {
                    log('❌ Could not convert to Kociemba format', 'error');
                    log('This might be due to invalid cube orientation', 'error');
                    return;
                }

                log(`Kociemba: ${kociembaString.substring(0, 20)}...`, 'info');
                log(`Kociemba length: ${kociembaString.length}`, 'info');

                // Try to solve
                log('Sending to solver...', 'info');
                const response = await fetch('http://localhost:5000/solve', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cube_string: kociembaString })
                });

                log(`Server response status: ${response.status}`, 'info');

                if (response.ok) {
                    const data = await response.json();
                    log(`Server response: ${JSON.stringify(data, null, 2)}`, 'info');

                    if (data.solution) {
                        log(`Solution: ${data.solution}`, 'success');
                        log(`Solution length: ${data.move_count} moves`, 'info');

                        // Apply solution
                        log('Applying solution...', 'info');
                        cube.applySolution(data.solution);

                        if (cube.isSolved()) {
                            log('🎉 Cube solved successfully!', 'success');
                        } else {
                            log('❌ Solution did not solve the cube', 'error');
                            log(`Final state: ${cube.cubeString.substring(0, 20)}...`, 'error');
                        }
                    } else if (data.error) {
                        log(`❌ Server error: ${data.error}`, 'error');
                    } else {
                        log('❌ No solution received', 'error');
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ Server error ${response.status}: ${errorText}`, 'error');
                }

            } catch (error) {
                log(`Error: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }
    </script>
</body>
</html>
