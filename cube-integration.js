/**
 * Integration wrapper to make CleanCube compatible with existing interface
 * This allows the clean implementation to work with the existing UI
 */

class CubeState {
    constructor(size = 3) {
        this.size = size;
        if (size === 3) {
            this.cleanCube = new CleanCube();
        } else {
            // For non-3x3 cubes, use original implementation
            this.initializeOriginal(size);
        }
    }

    initializeOriginal(size) {
        // Original initialization for 2x2 and 4x4
        this.colors = {
            'U': 'white',   'D': 'yellow',  'F': 'green',
            'B': 'blue',    'R': 'red',     'L': 'orange'
        };

        this.colorToChar = {
            'white': 'W',   'yellow': 'Y',  'green': 'G',
            'blue': 'B',    'red': 'R',     'orange': 'O'
        };

        this.resetToSolved();
    }

    resetToSolved() {
        if (this.size === 3) {
            this.cleanCube.reset();
        } else {
            // Original implementation for other sizes
            const stickersPerFace = this.size * this.size;
            const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
            
            this.cubeString = '';
            faceOrder.forEach(face => {
                const color = this.colors[face];
                const char = this.colorToChar[color];
                this.cubeString += char.repeat(stickersPerFace);
            });
        }
    }

    get cubeString() {
        if (this.size === 3) {
            return this.cleanCube.getDisplayString();
        } else {
            return this._cubeString;
        }
    }

    set cubeString(value) {
        if (this.size !== 3) {
            this._cubeString = value;
        }
    }

    executeMove(face, clockwise = true) {
        if (this.size === 3) {
            const move = face + (clockwise ? '' : "'");
            this.cleanCube.applyMoves(move);
            return this.cubeString;
        } else {
            // Use original implementation for other sizes
            console.log(`Move ${face}${clockwise ? '' : "'"} not implemented for size ${this.size}`);
            return this.cubeString;
        }
    }

    applyScramble(scrambleString) {
        if (this.size === 3) {
            this.cleanCube.applyMoves(scrambleString);
            return this.cubeString;
        } else {
            // Original implementation for other sizes
            console.log(`Scramble not implemented for size ${this.size}`);
            return this.cubeString;
        }
    }

    applySolution(solutionString) {
        return this.applyScramble(solutionString);
    }

    generateScramble(moveCount = 20) {
        if (this.size === 3) {
            return this.cleanCube.generateScramble(moveCount);
        } else {
            // Simple scramble for other sizes
            const faces = ['U', 'D', 'F', 'B', 'R', 'L'];
            const modifiers = ['', "'", '2'];
            const scramble = [];
            let lastFace = '';

            for (let i = 0; i < moveCount; i++) {
                let face;
                do {
                    face = faces[Math.floor(Math.random() * faces.length)];
                } while (face === lastFace && faces.length > 1);
                
                const modifier = modifiers[Math.floor(Math.random() * modifiers.length)];
                scramble.push(face + modifier);
                lastFace = face;
            }

            return scramble.join(' ');
        }
    }

    isSolved() {
        if (this.size === 3) {
            return this.cleanCube.isSolved();
        } else {
            // Original implementation for other sizes
            const stickersPerFace = this.size * this.size;
            const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
            
            for (let faceIndex = 0; faceIndex < 6; faceIndex++) {
                const startPos = faceIndex * stickersPerFace;
                const firstSticker = this.cubeString[startPos];
                
                for (let i = 1; i < stickersPerFace; i++) {
                    if (this.cubeString[startPos + i] !== firstSticker) {
                        return false;
                    }
                }
            }
            
            return true;
        }
    }

    getKociembaString() {
        if (this.size === 3) {
            return this.cleanCube.getKociembaString();
        } else {
            return null; // Only 3x3 supports Kociemba
        }
    }

    // Additional methods for compatibility
    applyMove(moveString) {
        const face = moveString[0];
        const clockwise = !moveString.includes("'");
        return this.executeMove(face, clockwise);
    }

    validateCubeState() {
        if (this.size === 3) {
            return this.cleanCube.isValidState();
        } else {
            return true; // Assume valid for other sizes
        }
    }

    // Methods that might be used by the UI
    stringToFaces() {
        // Convert string to face structure for compatibility
        const faces = {};
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        const stickersPerFace = this.size * this.size;

        faceOrder.forEach((face, faceIndex) => {
            faces[face] = [];
            for (let row = 0; row < this.size; row++) {
                faces[face][row] = [];
                for (let col = 0; col < this.size; col++) {
                    const stringIndex = faceIndex * stickersPerFace + row * this.size + col;
                    const char = this.cubeString[stringIndex];
                    faces[face][row][col] = char;
                }
            }
        });

        return faces;
    }

    facesToString(faces) {
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        let result = '';

        faceOrder.forEach(face => {
            for (let row = 0; row < this.size; row++) {
                for (let col = 0; col < this.size; col++) {
                    result += faces[face][row][col];
                }
            }
        });

        return result;
    }
}

// Make sure CleanCube is available
if (typeof CleanCube === 'undefined' && typeof window !== 'undefined') {
    console.error('CleanCube not found. Make sure cube-solver-clean.js is loaded first.');
}

// Export for different environments
if (typeof window !== 'undefined') {
    window.CubeState = CubeState;
} else {
    module.exports = { CubeState };
}

// Test function
function testIntegration() {
    console.log('=== Testing Integration Wrapper ===');
    
    const cube = new CubeState(3);
    console.log('Initial state:', cube.cubeString);
    console.log('Is solved:', cube.isSolved());
    
    // Test scramble
    const scramble = cube.generateScramble(5);
    console.log('Generated scramble:', scramble);
    
    cube.applyScramble(scramble);
    console.log('After scramble:', cube.cubeString.substring(0, 20) + '...');
    console.log('Is solved:', cube.isSolved());
    
    // Test Kociemba
    const kociemba = cube.getKociembaString();
    console.log('Kociemba:', kociemba);
    
    return kociemba && kociemba.length === 54;
}

if (typeof window !== 'undefined') {
    window.testIntegration = testIntegration;
}
