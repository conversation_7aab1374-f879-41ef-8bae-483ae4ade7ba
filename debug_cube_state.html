<!DOCTYPE html>
<html>
<head>
    <title>Debug Cube State</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; }
        button { margin: 5px; padding: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 Debug Cube State</h1>
    
    <button onclick="debugCubeState()">Debug Cube State</button>
    <button onclick="validateCubeConfiguration()">Validate Cube Configuration</button>
    <div id="results"></div>

    <script src="js/cube-state.js"></script>
    <script>
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function debugCubeState() {
            clearResults();
            log('🔍 Debugging cube state step by step...', 'info');

            try {
                const cube = new CubeState(3);
                
                // Step 1: Check initial state
                log('Step 1: Initial solved state', 'info');
                log(`<pre>Initial: ${cube.cubeString}</pre>`, 'info');
                
                const initialFaces = cube.stringToFaces();
                log('Initial face centers:', 'info');
                log(`<pre>U: ${initialFaces.U[1][1]}, R: ${initialFaces.R[1][1]}, F: ${initialFaces.F[1][1]}, D: ${initialFaces.D[1][1]}, L: ${initialFaces.L[1][1]}, B: ${initialFaces.B[1][1]}</pre>`, 'info');
                
                // Step 2: Apply single R move
                log('Step 2: Applying R move', 'info');
                cube.executeMove('R', true);
                log(`<pre>After R: ${cube.cubeString}</pre>`, 'info');
                
                const afterR = cube.stringToFaces();
                log('Centers after R:', 'info');
                log(`<pre>U: ${afterR.U[1][1]}, R: ${afterR.R[1][1]}, F: ${afterR.F[1][1]}, D: ${afterR.D[1][1]}, L: ${afterR.L[1][1]}, B: ${afterR.B[1][1]}</pre>`, 'info');
                
                // Check if centers moved (they shouldn't)
                if (initialFaces.U[1][1] !== afterR.U[1][1] || 
                    initialFaces.R[1][1] !== afterR.R[1][1] ||
                    initialFaces.F[1][1] !== afterR.F[1][1] ||
                    initialFaces.D[1][1] !== afterR.D[1][1] ||
                    initialFaces.L[1][1] !== afterR.L[1][1] ||
                    initialFaces.B[1][1] !== afterR.B[1][1]) {
                    log('❌ ERROR: Centers moved during R move!', 'error');
                } else {
                    log('✅ Centers preserved during R move', 'success');
                }
                
                // Step 3: Apply U move
                log('Step 3: Applying U move', 'info');
                cube.executeMove('U', true);
                log(`<pre>After U: ${cube.cubeString}</pre>`, 'info');
                
                // Step 4: Apply R' move
                log('Step 4: Applying R\' move', 'info');
                cube.executeMove('R', false);
                log(`<pre>After R': ${cube.cubeString}</pre>`, 'info');
                
                // Step 5: Apply U' move
                log('Step 5: Applying U\' move', 'info');
                cube.executeMove('U', false);
                log(`<pre>After U': ${cube.cubeString}</pre>`, 'info');
                
                const finalFaces = cube.stringToFaces();
                log('Final centers:', 'info');
                log(`<pre>U: ${finalFaces.U[1][1]}, R: ${finalFaces.R[1][1]}, F: ${finalFaces.F[1][1]}, D: ${finalFaces.D[1][1]}, L: ${finalFaces.L[1][1]}, B: ${finalFaces.B[1][1]}</pre>`, 'info');
                
                // Step 6: Try Kociemba conversion
                log('Step 6: Converting to Kociemba format', 'info');
                const kociembaString = cube.getKociembaString();
                if (kociembaString) {
                    log(`<pre>Kociemba: ${kociembaString}</pre>`, 'success');
                    log(`Length: ${kociembaString.length}`, 'info');
                    
                    // Validate Kociemba string
                    const charCounts = {};
                    for (const char of kociembaString) {
                        charCounts[char] = (charCounts[char] || 0) + 1;
                    }
                    log(`Character counts: <pre>${JSON.stringify(charCounts, null, 2)}</pre>`, 'info');
                    
                    // Each character should appear exactly 9 times
                    const expectedChars = ['U', 'R', 'F', 'D', 'L', 'B'];
                    let validCounts = true;
                    for (const char of expectedChars) {
                        if (charCounts[char] !== 9) {
                            log(`❌ Invalid count for ${char}: ${charCounts[char]} (expected 9)`, 'error');
                            validCounts = false;
                        }
                    }
                    
                    if (validCounts) {
                        log('✅ Kociemba string has valid character counts', 'success');
                    }
                    
                } else {
                    log('❌ Failed to convert to Kociemba format', 'error');
                }
                
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }

        function validateCubeConfiguration() {
            clearResults();
            log('🔍 Validating cube configuration...', 'info');

            try {
                const cube = new CubeState(3);
                
                // Apply the problematic sequence
                cube.applyScramble('R U R\' U\'');
                
                log('Cube state after R U R\' U\':', 'info');
                log(`<pre>${cube.cubeString}</pre>`, 'info');
                
                // Check if this is a valid cube configuration
                // Count each color
                const colorCounts = {};
                for (const char of cube.cubeString) {
                    colorCounts[char] = (colorCounts[char] || 0) + 1;
                }
                
                log('Color distribution:', 'info');
                log(`<pre>${JSON.stringify(colorCounts, null, 2)}</pre>`, 'info');
                
                // Each color should appear exactly 9 times
                const expectedColors = ['W', 'R', 'G', 'Y', 'O', 'B'];
                let validDistribution = true;
                for (const color of expectedColors) {
                    if (colorCounts[color] !== 9) {
                        log(`❌ Invalid count for ${color}: ${colorCounts[color]} (expected 9)`, 'error');
                        validDistribution = false;
                    }
                }
                
                if (validDistribution) {
                    log('✅ Color distribution is valid', 'success');
                } else {
                    log('❌ Invalid color distribution - cube configuration is impossible', 'error');
                }
                
                // Check face structure
                const faces = cube.stringToFaces();
                log('Face analysis:', 'info');
                for (const [faceName, faceArray] of Object.entries(faces)) {
                    const center = faceArray[1][1];
                    const edges = [faceArray[0][1], faceArray[1][0], faceArray[1][2], faceArray[2][1]];
                    const corners = [faceArray[0][0], faceArray[0][2], faceArray[2][0], faceArray[2][2]];
                    
                    log(`${faceName} face - Center: ${center}, Edges: [${edges.join(',')}], Corners: [${corners.join(',')}]`, 'info');
                }
                
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }
    </script>
</body>
</html>
