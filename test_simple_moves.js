// Simple test to verify the cube moves work correctly

// Test the R move specifically
function testRMove() {
    console.log('=== Testing R Move ===');
    
    // Create a solved cube string: WWWWWWWWWRRRRRRRRRGGGGGGGGGYYYYYYYYYOOOOOOOOOBBBBBBBBB
    // Positions: U(0-8), R(9-17), F(18-26), D(27-35), L(36-44), B(45-53)
    let cubeString = 'WWWWWWWWWRRRRRRRRRGGGGGGGGGYYYYYYYYYOOOOOOOOOBBBBBBBBB';
    
    console.log('Initial cube:', cubeString);
    
    // For R move, the cycle should be:
    // U right column [2,5,8] → B left column [47,50,53] (reversed) → D right column [35,32,29] → F right column [26,23,20] → U right column
    
    // Before R move:
    // U right: W W W (positions 2,5,8)
    // F right: G G G (positions 26,23,20) 
    // D right: Y Y Y (positions 35,32,29)
    // B left:  B B B (positions 47,50,53)
    
    console.log('Before R move:');
    console.log('U right column:', [cubeString[2], cubeString[5], cubeString[8]]);
    console.log('F right column:', [cubeString[26], cubeString[23], cubeString[20]]);
    console.log('D right column:', [cubeString[35], cubeString[32], cubeString[29]]);
    console.log('B left column:', [cubeString[47], cubeString[50], cubeString[53]]);
    
    // Apply R move manually
    // Cycle: U→B(rev), F→U, D→F, B(rev)→D
    const uRight = [cubeString[2], cubeString[5], cubeString[8]];
    const fRight = [cubeString[26], cubeString[23], cubeString[20]];
    const dRight = [cubeString[35], cubeString[32], cubeString[29]];
    const bLeft = [cubeString[47], cubeString[50], cubeString[53]];
    
    // After R move:
    // U right ← F right
    // F right ← D right  
    // D right ← B left (reversed)
    // B left ← U right (reversed)
    
    let newCube = cubeString.split('');
    
    // U right ← F right
    newCube[2] = fRight[0];
    newCube[5] = fRight[1];
    newCube[8] = fRight[2];
    
    // F right ← D right
    newCube[26] = dRight[0];
    newCube[23] = dRight[1];
    newCube[20] = dRight[2];
    
    // D right ← B left (reversed)
    newCube[35] = bLeft[2];
    newCube[32] = bLeft[1];
    newCube[29] = bLeft[0];
    
    // B left ← U right (reversed)
    newCube[47] = uRight[2];
    newCube[50] = uRight[1];
    newCube[53] = uRight[0];
    
    // Rotate R face itself
    const rFace = [
        cubeString[9], cubeString[10], cubeString[11],
        cubeString[12], cubeString[13], cubeString[14],
        cubeString[15], cubeString[16], cubeString[17]
    ];
    
    // Clockwise rotation: 0→2, 1→5, 2→8, 3→1, 4→4, 5→7, 6→0, 7→3, 8→6
    newCube[9] = rFace[6];   // 0 ← 6
    newCube[10] = rFace[3];  // 1 ← 3
    newCube[11] = rFace[0];  // 2 ← 0
    newCube[12] = rFace[7];  // 3 ← 7
    newCube[13] = rFace[4];  // 4 ← 4 (center)
    newCube[14] = rFace[1];  // 5 ← 1
    newCube[15] = rFace[8];  // 6 ← 8
    newCube[16] = rFace[5];  // 7 ← 5
    newCube[17] = rFace[2];  // 8 ← 2
    
    const resultString = newCube.join('');
    
    console.log('After R move:', resultString);
    console.log('Expected after R move:');
    console.log('U right column:', [resultString[2], resultString[5], resultString[8]]);
    console.log('F right column:', [resultString[26], resultString[23], resultString[20]]);
    console.log('D right column:', [resultString[35], resultString[32], resultString[29]]);
    console.log('B left column:', [resultString[47], resultString[50], resultString[53]]);
    
    return resultString;
}

// Test R R' sequence
function testRRPrime() {
    console.log('\n=== Testing R R\' Sequence ===');
    
    const initial = 'WWWWWWWWWRRRRRRRRRGGGGGGGGGYYYYYYYYYOOOOOOOOOBBBBBBBBB';
    console.log('Initial:', initial);
    
    // Apply R move
    const afterR = testRMove();
    
    // Now apply R' (reverse R move)
    console.log('\nApplying R\' move...');
    
    // For R' move, reverse the cycle:
    // U right ← B left (reversed)
    // F right ← U right
    // D right ← F right
    // B left ← D right (reversed)
    
    let cube = afterR.split('');
    
    const uRight = [cube[2], cube[5], cube[8]];
    const fRight = [cube[26], cube[23], cube[20]];
    const dRight = [cube[35], cube[32], cube[29]];
    const bLeft = [cube[47], cube[50], cube[53]];
    
    // Apply reverse cycle
    let newCube = cube.slice();
    
    // U right ← B left (reversed)
    newCube[2] = bLeft[2];
    newCube[5] = bLeft[1];
    newCube[8] = bLeft[0];
    
    // F right ← U right
    newCube[26] = uRight[0];
    newCube[23] = uRight[1];
    newCube[20] = uRight[2];
    
    // D right ← F right
    newCube[35] = fRight[0];
    newCube[32] = fRight[1];
    newCube[29] = fRight[2];
    
    // B left ← D right (reversed)
    newCube[47] = dRight[2];
    newCube[50] = dRight[1];
    newCube[53] = dRight[0];
    
    // Rotate R face counterclockwise
    const rFace = [
        cube[9], cube[10], cube[11],
        cube[12], cube[13], cube[14],
        cube[15], cube[16], cube[17]
    ];
    
    // Counterclockwise rotation: 0→6, 1→3, 2→0, 3→7, 4→4, 5→1, 6→8, 7→5, 8→2
    newCube[9] = rFace[2];   // 0 ← 2
    newCube[10] = rFace[5];  // 1 ← 5
    newCube[11] = rFace[8];  // 2 ← 8
    newCube[12] = rFace[1];  // 3 ← 1
    newCube[13] = rFace[4];  // 4 ← 4 (center)
    newCube[14] = rFace[7];  // 5 ← 7
    newCube[15] = rFace[0];  // 6 ← 0
    newCube[16] = rFace[3];  // 7 ← 3
    newCube[17] = rFace[6];  // 8 ← 6
    
    const final = newCube.join('');
    
    console.log('After R\':', final);
    console.log('Initial: ', initial);
    console.log('Final:   ', final);
    console.log('Match:   ', initial === final);
    
    return initial === final;
}

// Run the test
if (typeof window === 'undefined') {
    // Node.js
    const result = testRRPrime();
    console.log('\n=== RESULT ===');
    console.log('R R\' test:', result ? 'PASSED' : 'FAILED');
} else {
    // Browser
    window.testRMove = testRMove;
    window.testRRPrime = testRRPrime;
}
