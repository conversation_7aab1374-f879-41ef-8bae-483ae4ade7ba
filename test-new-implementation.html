<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Implementation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-section { 
            margin: 20px 0; 
            padding: 20px; 
            background: white; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { 
            margin: 5px; 
            padding: 12px 20px; 
            border: none; 
            border-radius: 5px; 
            background: #007bff; 
            color: white; 
            cursor: pointer;
            font-weight: 600;
        }
        button:hover { background: #0056b3; }
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto; 
            border: 1px solid #e9ecef;
        }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
    </style>
</head>
<body>
    <h1>🧩 New Rubik's Cube Implementation Test Suite</h1>
    
    <div class="test-grid">
        <div class="test-section">
            <h2>🔧 Core Functionality Tests</h2>
            <button onclick="testCubeStateBasics()">Test Cube State Basics</button>
            <button onclick="testMoveExecution()">Test Move Execution</button>
            <button onclick="testMoveSequences()">Test Move Sequences</button>
            <div id="core-result"></div>
        </div>
        
        <div class="test-section">
            <h2>🎲 3x3 Cube Tests</h2>
            <button onclick="test3x3BasicMoves()">Test Basic Moves</button>
            <button onclick="test3x3Algorithms()">Test Algorithms</button>
            <button onclick="test3x3ScrambleAndSolve()">Test Scramble & Solve</button>
            <div id="test3x3-result"></div>
        </div>
        
        <div class="test-section">
            <h2>🎯 2x2 Cube Tests</h2>
            <button onclick="test2x2Moves()">Test 2x2 Moves</button>
            <button onclick="test2x2Solver()">Test 2x2 Solver</button>
            <div id="test2x2-result"></div>
        </div>
        
        <div class="test-section">
            <h2>🚀 4x4 Cube Tests</h2>
            <button onclick="test4x4Moves()">Test 4x4 Moves</button>
            <button onclick="test4x4WideMoves()">Test Wide Moves</button>
            <button onclick="test4x4Solver()">Test 4x4 Solver</button>
            <div id="test4x4-result"></div>
        </div>
        
        <div class="test-section">
            <h2>🌐 Server Integration Tests</h2>
            <button onclick="testServerConnection()">Test Server Connection</button>
            <button onclick="testAllSolvers()">Test All Solvers</button>
            <div id="server-result"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 Performance Tests</h2>
            <button onclick="testPerformance()">Test Performance</button>
            <button onclick="testMemoryUsage()">Test Memory Usage</button>
            <div id="performance-result"></div>
        </div>
    </div>

    <script src="js/cube-state-new.js"></script>
    <script>
        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            element.appendChild(div);
        }

        function clearResults(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        // Core Functionality Tests
        function testCubeStateBasics() {
            const resultId = 'core-result';
            clearResults(resultId);
            
            try {
                logResult(resultId, '🔍 Testing cube state basics...', 'info');
                
                // Test 3x3 initialization
                const cube3x3 = new CubeState(3);
                if (cube3x3.cubeString.length === 54) {
                    logResult(resultId, '✅ 3x3 cube initialized correctly (54 stickers)', 'success');
                } else {
                    logResult(resultId, `❌ 3x3 cube wrong size: ${cube3x3.cubeString.length}`, 'error');
                }
                
                // Test 2x2 initialization
                const cube2x2 = new CubeState(2);
                if (cube2x2.cubeString.length === 24) {
                    logResult(resultId, '✅ 2x2 cube initialized correctly (24 stickers)', 'success');
                } else {
                    logResult(resultId, `❌ 2x2 cube wrong size: ${cube2x2.cubeString.length}`, 'error');
                }
                
                // Test 4x4 initialization
                const cube4x4 = new CubeState(4);
                if (cube4x4.cubeString.length === 96) {
                    logResult(resultId, '✅ 4x4 cube initialized correctly (96 stickers)', 'success');
                } else {
                    logResult(resultId, `❌ 4x4 cube wrong size: ${cube4x4.cubeString.length}`, 'error');
                }
                
                // Test solved state
                if (cube3x3.isSolved()) {
                    logResult(resultId, '✅ New cube is correctly identified as solved', 'success');
                } else {
                    logResult(resultId, '❌ New cube not identified as solved', 'error');
                }
                
                logResult(resultId, '✅ All basic tests passed!', 'success');
                
            } catch (error) {
                logResult(resultId, `❌ Error: ${error.message}`, 'error');
            }
        }

        function testMoveExecution() {
            const resultId = 'core-result';
            
            try {
                logResult(resultId, '🔄 Testing move execution...', 'info');
                
                const cube = new CubeState(3);
                const initialState = cube.cubeString;
                
                // Test single move
                cube.executeMove('R', true);
                if (cube.cubeString !== initialState) {
                    logResult(resultId, '✅ R move changes cube state', 'success');
                } else {
                    logResult(resultId, '❌ R move does not change cube state', 'error');
                }
                
                // Test move reversal
                cube.executeMove('R', false);
                if (cube.cubeString === initialState) {
                    logResult(resultId, '✅ R R\' returns to original state', 'success');
                } else {
                    logResult(resultId, '❌ R R\' does not return to original state', 'error');
                    logResult(resultId, `Expected: ${initialState.substring(0, 20)}...`, 'info');
                    logResult(resultId, `Got: ${cube.cubeString.substring(0, 20)}...`, 'info');
                }
                
            } catch (error) {
                logResult(resultId, `❌ Move execution error: ${error.message}`, 'error');
            }
        }

        function testMoveSequences() {
            const resultId = 'core-result';
            
            try {
                logResult(resultId, '🔄 Testing move sequences...', 'info');
                
                const cube = new CubeState(3);
                const initialState = cube.cubeString;
                
                // Test sexy move (should return to original after 6 repetitions)
                const sexyMove = 'R U R\' U\'';
                for (let i = 0; i < 6; i++) {
                    cube.applyMoves(sexyMove);
                }
                
                if (cube.cubeString === initialState) {
                    logResult(resultId, '✅ Sexy move cycle (6x) returns to original state', 'success');
                } else {
                    logResult(resultId, '❌ Sexy move cycle does not return to original state', 'error');
                }
                
                // Test scramble and unscramble
                cube.resetToSolved();
                const scramble = 'R U R\' U\' R\' F R2 U\' R\' U\' R U R\' F\'';
                cube.applyMoves(scramble);
                
                if (!cube.isSolved()) {
                    logResult(resultId, '✅ Scramble successfully scrambles cube', 'success');
                } else {
                    logResult(resultId, '❌ Scramble does not scramble cube', 'error');
                }
                
            } catch (error) {
                logResult(resultId, `❌ Move sequence error: ${error.message}`, 'error');
            }
        }

        // 3x3 Tests
        function test3x3BasicMoves() {
            const resultId = 'test3x3-result';
            clearResults(resultId);
            
            try {
                logResult(resultId, '🎲 Testing 3x3 basic moves...', 'info');
                
                const cube = new CubeState(3);
                const moves = ['R', 'U', 'F', 'L', 'D', 'B'];
                let allPassed = true;
                
                for (const move of moves) {
                    const initialState = cube.cubeString;
                    
                    // Test clockwise
                    cube.executeMove(move, true);
                    const afterClockwise = cube.cubeString;
                    
                    // Test counterclockwise
                    cube.executeMove(move, false);
                    const afterCounterclockwise = cube.cubeString;
                    
                    if (afterCounterclockwise === initialState) {
                        logResult(resultId, `✅ ${move} and ${move}' work correctly`, 'success');
                    } else {
                        logResult(resultId, `❌ ${move} and ${move}' do not cancel out`, 'error');
                        allPassed = false;
                    }
                }
                
                if (allPassed) {
                    logResult(resultId, '🎉 All 3x3 basic moves work correctly!', 'success');
                }
                
            } catch (error) {
                logResult(resultId, `❌ Error: ${error.message}`, 'error');
            }
        }

        function test3x3Algorithms() {
            const resultId = 'test3x3-result';
            
            try {
                logResult(resultId, '🧩 Testing 3x3 algorithms...', 'info');
                
                const cube = new CubeState(3);
                
                // Test T-Perm (should return to solved after 1 execution on solved cube)
                const tPerm = 'R U R\' F\' R U R\' U\' R\' F R2 U\' R\'';
                cube.applyMoves(tPerm);
                
                // T-Perm on solved cube should still be solved (it's a PLL algorithm)
                if (cube.isSolved()) {
                    logResult(resultId, '✅ T-Perm on solved cube maintains solved state', 'success');
                } else {
                    logResult(resultId, '⚠️ T-Perm on solved cube changes state (expected for some algorithms)', 'warning');
                }
                
                // Test algorithm parsing
                cube.resetToSolved();
                cube.applyMoves('R U2 R\' D\' R U\' R\' D');
                
                if (!cube.isSolved()) {
                    logResult(resultId, '✅ Complex algorithm parsing works', 'success');
                } else {
                    logResult(resultId, '⚠️ Complex algorithm had no effect', 'warning');
                }
                
            } catch (error) {
                logResult(resultId, `❌ Algorithm error: ${error.message}`, 'error');
            }
        }

        async function test3x3ScrambleAndSolve() {
            const resultId = 'test3x3-result';
            
            try {
                logResult(resultId, '🔄 Testing 3x3 scramble and solve...', 'info');
                
                const cube = new CubeState(3);
                
                // Generate and apply scramble
                const scramble = cube.generateScramble(15);
                logResult(resultId, `Generated scramble: ${scramble}`, 'info');
                
                cube.applyScramble(scramble);
                
                if (!cube.isSolved()) {
                    logResult(resultId, '✅ Scramble successfully scrambled cube', 'success');
                } else {
                    logResult(resultId, '❌ Scramble did not scramble cube', 'error');
                    return;
                }
                
                // Test Kociemba format conversion
                const kociembaString = cube.getKociembaString();
                if (kociembaString && kociembaString.length === 54) {
                    logResult(resultId, '✅ Kociemba format conversion successful', 'success');
                    logResult(resultId, `Kociemba string: ${kociembaString.substring(0, 20)}...`, 'info');
                } else {
                    logResult(resultId, '❌ Kociemba format conversion failed', 'error');
                    return;
                }
                
                // Test server solve
                try {
                    const response = await fetch('http://localhost:5000/solve', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ cube_string: kociembaString })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        if (data.solution) {
                            logResult(resultId, `✅ Server returned solution: ${data.solution.substring(0, 50)}...`, 'success');
                            
                            // Apply solution
                            cube.applySolution(data.solution);
                            
                            if (cube.isSolved()) {
                                logResult(resultId, '🎉 Solution successfully solved the cube!', 'success');
                            } else {
                                logResult(resultId, '❌ Solution did not solve the cube', 'error');
                            }
                        } else {
                            logResult(resultId, '❌ Server returned no solution', 'error');
                        }
                    } else {
                        logResult(resultId, `❌ Server error: ${response.status}`, 'error');
                    }
                } catch (serverError) {
                    logResult(resultId, `⚠️ Server not available: ${serverError.message}`, 'warning');
                }
                
            } catch (error) {
                logResult(resultId, `❌ Error: ${error.message}`, 'error');
            }
        }

        // 2x2 Tests
        function test2x2Moves() {
            const resultId = 'test2x2-result';
            clearResults(resultId);
            
            try {
                logResult(resultId, '🎯 Testing 2x2 moves...', 'info');
                
                const cube = new CubeState(2);
                const initialState = cube.cubeString;
                
                // Test basic move
                cube.executeMove('R', true);
                cube.executeMove('R', false);
                
                if (cube.cubeString === initialState) {
                    logResult(resultId, '✅ 2x2 R R\' returns to original state', 'success');
                } else {
                    logResult(resultId, '❌ 2x2 R R\' does not return to original state', 'error');
                }
                
                // Test scramble
                const scramble = cube.generateScramble(10);
                cube.applyScramble(scramble);
                
                if (!cube.isSolved()) {
                    logResult(resultId, '✅ 2x2 scramble works', 'success');
                } else {
                    logResult(resultId, '❌ 2x2 scramble does not work', 'error');
                }
                
            } catch (error) {
                logResult(resultId, `❌ Error: ${error.message}`, 'error');
            }
        }

        async function test2x2Solver() {
            const resultId = 'test2x2-result';
            
            try {
                logResult(resultId, '🔧 Testing 2x2 solver...', 'info');
                
                const cube = new CubeState(2);
                
                // Test with solved cube
                const response = await fetch('http://localhost:5000/solve', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cube_string: cube.cubeString })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    logResult(resultId, `✅ 2x2 solver responded: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    logResult(resultId, `❌ 2x2 solver error: ${response.status}`, 'error');
                }
                
            } catch (error) {
                logResult(resultId, `⚠️ Server not available: ${error.message}`, 'warning');
            }
        }

        // Add more test functions for 4x4, server, and performance...
        // (Due to length constraints, I'll add these in the next part)
    </script>
</body>
</html>
