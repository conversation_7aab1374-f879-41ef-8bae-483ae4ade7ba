// Test all cube moves systematically

function testAllMoves() {
    console.log('=== Testing All Cube Moves ===');
    
    const moves = ['U', 'D', 'F', 'B', 'R', 'L'];
    let allPassed = true;
    
    for (const move of moves) {
        console.log(`\nTesting ${move} move...`);
        
        const initial = 'WWWWWWWWWRRRRRRRRRGGGGGGGGGYYYYYYYYYOOOOOOOOOBBBBBBBBB';
        
        // Test move and its inverse
        const result = testSingleMove(move, initial);
        
        if (result) {
            console.log(`✓ ${move} move works correctly`);
        } else {
            console.log(`✗ ${move} move failed`);
            allPassed = false;
        }
    }
    
    console.log('\n=== FINAL RESULT ===');
    console.log('All moves test:', allPassed ? 'PASSED' : 'FAILED');
    
    return allPassed;
}

function testSingleMove(move, initialString) {
    // Apply move and its inverse, should return to initial state
    
    // For testing, we'll simulate the moves manually based on correct logic
    const moveImplementations = {
        'R': (cube) => applyRMove(cube, true),
        'L': (cube) => applyLMove(cube, true),
        'U': (cube) => applyUMove(cube, true),
        'D': (cube) => applyDMove(cube, true),
        'F': (cube) => applyFMove(cube, true),
        'B': (cube) => applyBMove(cube, true)
    };
    
    const inverseImplementations = {
        'R': (cube) => applyRMove(cube, false),
        'L': (cube) => applyLMove(cube, false),
        'U': (cube) => applyUMove(cube, false),
        'D': (cube) => applyDMove(cube, false),
        'F': (cube) => applyFMove(cube, false),
        'B': (cube) => applyBMove(cube, false)
    };
    
    let cubeString = initialString;
    
    // Apply move
    cubeString = moveImplementations[move](cubeString);
    console.log(`After ${move}:`, cubeString.substring(0, 20) + '...');
    
    // Apply inverse
    cubeString = inverseImplementations[move](cubeString);
    console.log(`After ${move}':`, cubeString.substring(0, 20) + '...');
    
    const success = cubeString === initialString;
    console.log(`${move} ${move}' test:`, success ? 'PASSED' : 'FAILED');
    
    return success;
}

// Manual implementations based on correct position mappings
function applyRMove(cubeString, clockwise) {
    let cube = cubeString.split('');
    
    // R move cycle: U right [2,5,8] → F right [26,23,20] → D right [35,32,29] → B left [53,50,47] → U right
    const uRight = [cube[2], cube[5], cube[8]];
    const fRight = [cube[26], cube[23], cube[20]];
    const dRight = [cube[35], cube[32], cube[29]];
    const bLeft = [cube[53], cube[50], cube[47]];
    
    if (clockwise) {
        // U ← F, F ← D, D ← B, B ← U
        [cube[2], cube[5], cube[8]] = fRight;
        [cube[26], cube[23], cube[20]] = dRight;
        [cube[35], cube[32], cube[29]] = bLeft;
        [cube[53], cube[50], cube[47]] = uRight;
    } else {
        // Reverse: U ← B, F ← U, D ← F, B ← D
        [cube[2], cube[5], cube[8]] = bLeft;
        [cube[26], cube[23], cube[20]] = uRight;
        [cube[35], cube[32], cube[29]] = fRight;
        [cube[53], cube[50], cube[47]] = dRight;
    }
    
    // Rotate R face
    cube = rotateFace(cube, 'R', clockwise);
    
    return cube.join('');
}

function applyUMove(cubeString, clockwise) {
    let cube = cubeString.split('');
    
    // U move cycle: F top [18,19,20] → L top [36,37,38] → B top [45,46,47] → R top [9,10,11] → F top
    const fTop = [cube[18], cube[19], cube[20]];
    const lTop = [cube[36], cube[37], cube[38]];
    const bTop = [cube[45], cube[46], cube[47]];
    const rTop = [cube[9], cube[10], cube[11]];
    
    if (clockwise) {
        // F ← R, L ← F, B ← L, R ← B
        [cube[18], cube[19], cube[20]] = rTop;
        [cube[36], cube[37], cube[38]] = fTop;
        [cube[45], cube[46], cube[47]] = lTop;
        [cube[9], cube[10], cube[11]] = bTop;
    } else {
        // Reverse: F ← L, L ← B, B ← R, R ← F
        [cube[18], cube[19], cube[20]] = lTop;
        [cube[36], cube[37], cube[38]] = bTop;
        [cube[45], cube[46], cube[47]] = rTop;
        [cube[9], cube[10], cube[11]] = fTop;
    }
    
    // Rotate U face
    cube = rotateFace(cube, 'U', clockwise);
    
    return cube.join('');
}

// Placeholder implementations for other moves
function applyLMove(cubeString, clockwise) {
    // L move: U left [0,3,6] → F left [18,21,24] → D left [27,30,33] → B right [53,50,47] → U left
    let cube = cubeString.split('');
    
    const uLeft = [cube[0], cube[3], cube[6]];
    const fLeft = [cube[18], cube[21], cube[24]];
    const dLeft = [cube[27], cube[30], cube[33]];
    const bRight = [cube[53], cube[50], cube[47]]; // reversed
    
    if (clockwise) {
        [cube[0], cube[3], cube[6]] = fLeft;
        [cube[18], cube[21], cube[24]] = dLeft;
        [cube[27], cube[30], cube[33]] = bRight;
        [cube[53], cube[50], cube[47]] = uLeft;
    } else {
        [cube[0], cube[3], cube[6]] = bRight;
        [cube[18], cube[21], cube[24]] = uLeft;
        [cube[27], cube[30], cube[33]] = fLeft;
        [cube[53], cube[50], cube[47]] = dLeft;
    }
    
    cube = rotateFace(cube, 'L', clockwise);
    return cube.join('');
}

function applyDMove(cubeString, clockwise) {
    // D move: F bottom [24,25,26] → R bottom [15,16,17] → B bottom [51,52,53] → L bottom [42,43,44] → F bottom
    let cube = cubeString.split('');
    
    const fBottom = [cube[24], cube[25], cube[26]];
    const rBottom = [cube[15], cube[16], cube[17]];
    const bBottom = [cube[51], cube[52], cube[53]];
    const lBottom = [cube[42], cube[43], cube[44]];
    
    if (clockwise) {
        [cube[24], cube[25], cube[26]] = lBottom;
        [cube[15], cube[16], cube[17]] = fBottom;
        [cube[51], cube[52], cube[53]] = rBottom;
        [cube[42], cube[43], cube[44]] = bBottom;
    } else {
        [cube[24], cube[25], cube[26]] = rBottom;
        [cube[15], cube[16], cube[17]] = bBottom;
        [cube[51], cube[52], cube[53]] = lBottom;
        [cube[42], cube[43], cube[44]] = fBottom;
    }
    
    cube = rotateFace(cube, 'D', clockwise);
    return cube.join('');
}

function applyFMove(cubeString, clockwise) {
    // F move: U bottom [6,7,8] → R left [9,12,15] → D top [29,28,27] → L right [44,41,38] → U bottom
    let cube = cubeString.split('');
    
    const uBottom = [cube[6], cube[7], cube[8]];
    const rLeft = [cube[9], cube[12], cube[15]];
    const dTop = [cube[29], cube[28], cube[27]]; // reversed
    const lRight = [cube[44], cube[41], cube[38]]; // reversed
    
    if (clockwise) {
        [cube[6], cube[7], cube[8]] = lRight;
        [cube[9], cube[12], cube[15]] = uBottom;
        [cube[29], cube[28], cube[27]] = rLeft;
        [cube[44], cube[41], cube[38]] = dTop;
    } else {
        [cube[6], cube[7], cube[8]] = rLeft;
        [cube[9], cube[12], cube[15]] = dTop;
        [cube[29], cube[28], cube[27]] = lRight;
        [cube[44], cube[41], cube[38]] = uBottom;
    }
    
    cube = rotateFace(cube, 'F', clockwise);
    return cube.join('');
}

function applyBMove(cubeString, clockwise) {
    // B move: U top [2,1,0] → L left [36,39,42] → D bottom [33,34,35] → R right [17,14,11] → U top
    let cube = cubeString.split('');
    
    const uTop = [cube[2], cube[1], cube[0]]; // reversed
    const lLeft = [cube[36], cube[39], cube[42]];
    const dBottom = [cube[33], cube[34], cube[35]];
    const rRight = [cube[17], cube[14], cube[11]]; // reversed
    
    if (clockwise) {
        [cube[2], cube[1], cube[0]] = rRight;
        [cube[36], cube[39], cube[42]] = uTop;
        [cube[33], cube[34], cube[35]] = lLeft;
        [cube[17], cube[14], cube[11]] = dBottom;
    } else {
        [cube[2], cube[1], cube[0]] = lLeft;
        [cube[36], cube[39], cube[42]] = dBottom;
        [cube[33], cube[34], cube[35]] = rRight;
        [cube[17], cube[14], cube[11]] = uTop;
    }
    
    cube = rotateFace(cube, 'B', clockwise);
    return cube.join('');
}

function rotateFace(cube, face, clockwise) {
    const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
    const faceIndex = faceOrder.indexOf(face);
    const startPos = faceIndex * 9;
    
    const faceStickers = [];
    for (let i = 0; i < 9; i++) {
        faceStickers[i] = cube[startPos + i];
    }
    
    const rotationMap = clockwise ? 
        [6, 3, 0, 7, 4, 1, 8, 5, 2] :
        [2, 5, 8, 1, 4, 7, 0, 3, 6];
    
    const rotated = [...faceStickers];
    for (let i = 0; i < 9; i++) {
        if (i === 4) continue; // Skip center
        rotated[i] = faceStickers[rotationMap[i]];
    }
    
    for (let i = 0; i < 9; i++) {
        cube[startPos + i] = rotated[i];
    }
    
    return cube;
}

// Run test
if (typeof window === 'undefined') {
    testAllMoves();
} else {
    window.testAllMoves = testAllMoves;
}
