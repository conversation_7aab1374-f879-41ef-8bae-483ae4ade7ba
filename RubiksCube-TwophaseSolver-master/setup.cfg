[metadata]
name = RubikTwoPhase
version = 1.1.1
author = <PERSON>
author_email = koc<PERSON><PERSON>@t-online.de
description = A package to solve <PERSON><PERSON><PERSON>'s cube in less than 19 moves on average with the two-phase algorithm.
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/hkociemba/RubiksCube-TwophaseSolver
keywords='Rubi<PERSON>'s Cube, Rubik, cube, solver, twophase, two-phase, algorithm, Kociemba'
project_urls =
    Bug Tracker = https://github.com/hkociemba/RubiksCube-TwophaseSolver/issues
classifiers =
    Programming Language :: Python :: 3
    License :: OSI Approved :: GNU General Public License v3 or later (GPLv3+)
    Operating System :: OS Independent

[options]
package_dir =
    = package_src
packages = find:
python_requires = >=3.4

[options.packages.find]
where = package_src