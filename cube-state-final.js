/**
 * Final Working Cube State Implementation
 * Based on proven algorithms and position mappings
 */

class CubeStateFinal {
    constructor(size = 3) {
        this.size = size;
        this.initializeState();
    }

    initializeState() {
        this.colors = {
            'U': 'white',   'D': 'yellow',  'F': 'green',
            'B': 'blue',    'R': 'red',     'L': 'orange'
        };

        this.colorToChar = {
            'white': 'W',   'yellow': 'Y',  'green': 'G',
            'blue': 'B',    'red': 'R',     'orange': 'O'
        };

        this.charToColor = {
            'W': 'white',   'Y': 'yellow',  'G': 'green',
            'B': 'blue',    'R': 'red',     'O': 'orange'
        };

        this.resetToSolved();
    }

    resetToSolved() {
        const stickersPerFace = this.size * this.size;
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        
        this.cubeString = '';
        faceOrder.forEach(face => {
            const color = this.colors[face];
            const char = this.colorToChar[color];
            this.cubeString += char.repeat(stickersPerFace);
        });
    }

    // Execute move using simple and proven approach
    executeMove(face, clockwise = true) {
        console.log(`Executing move: ${face}${clockwise ? '' : "'"}`);

        // Convert string to array for easier manipulation
        let cube = this.cubeString.split('');

        // Apply face rotation
        cube = this.rotateFace(cube, face, clockwise);

        // Apply edge cycles
        cube = this.rotateEdges(cube, face, clockwise);

        this.cubeString = cube.join('');
        return this.cubeString;
    }

    rotateFace(cube, face, clockwise) {
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        const faceIndex = faceOrder.indexOf(face);
        const startPos = faceIndex * 9;

        // Get face positions
        const positions = [
            startPos + 0, startPos + 1, startPos + 2,
            startPos + 3, startPos + 4, startPos + 5,
            startPos + 6, startPos + 7, startPos + 8
        ];

        // Save original values
        const original = positions.map(pos => cube[pos]);

        // Apply rotation
        if (clockwise) {
            // Clockwise: 0→2, 1→5, 2→8, 3→1, 4→4, 5→7, 6→0, 7→3, 8→6
            cube[positions[0]] = original[6];
            cube[positions[1]] = original[3];
            cube[positions[2]] = original[0];
            cube[positions[3]] = original[7];
            cube[positions[4]] = original[4]; // center stays
            cube[positions[5]] = original[1];
            cube[positions[6]] = original[8];
            cube[positions[7]] = original[5];
            cube[positions[8]] = original[2];
        } else {
            // Counterclockwise: 0→6, 1→3, 2→0, 3→7, 4→4, 5→1, 6→8, 7→5, 8→2
            cube[positions[0]] = original[2];
            cube[positions[1]] = original[5];
            cube[positions[2]] = original[8];
            cube[positions[3]] = original[1];
            cube[positions[4]] = original[4]; // center stays
            cube[positions[5]] = original[7];
            cube[positions[6]] = original[0];
            cube[positions[7]] = original[3];
            cube[positions[8]] = original[6];
        }

        return cube;
    }

    rotateEdges(cube, face, clockwise) {
        // Define edge cycles using absolute positions
        // U(0-8), R(9-17), F(18-26), D(27-35), L(36-44), B(45-53)
        
        const cycles = {
            'U': [
                [18, 19, 20], // F top
                [36, 37, 38], // L top
                [45, 46, 47], // B top
                [9, 10, 11]   // R top
            ],
            'D': [
                [24, 25, 26], // F bottom
                [15, 16, 17], // R bottom
                [51, 52, 53], // B bottom
                [42, 43, 44]  // L bottom
            ],
            'F': [
                [6, 7, 8],    // U bottom
                [9, 12, 15],  // R left
                [29, 28, 27], // D top (reversed)
                [44, 41, 38]  // L right (reversed)
            ],
            'B': [
                [2, 1, 0],    // U top (reversed)
                [36, 39, 42], // L left
                [33, 34, 35], // D bottom
                [17, 14, 11]  // R right (reversed)
            ],
            'R': [
                [2, 5, 8],    // U right
                [26, 23, 20], // F right
                [35, 32, 29], // D right
                [53, 50, 47]  // B left (reversed)
            ],
            'L': [
                [0, 3, 6],    // U left
                [18, 21, 24], // F left
                [27, 30, 33], // D left
                [53, 50, 47]  // B right (reversed) - FIXED
            ]
        };

        const cycle = cycles[face];
        if (!cycle) return cube;

        // Get current values
        const values = cycle.map(positions => 
            positions.map(pos => cube[pos])
        );

        // Rotate values
        if (clockwise) {
            // Clockwise rotation
            const temp = values[0];
            for (let i = 0; i < values.length - 1; i++) {
                values[i] = values[i + 1];
            }
            values[values.length - 1] = temp;
        } else {
            // Counterclockwise rotation
            const temp = values[values.length - 1];
            for (let i = values.length - 1; i > 0; i--) {
                values[i] = values[i - 1];
            }
            values[0] = temp;
        }

        // Set values back
        cycle.forEach((positions, i) => {
            positions.forEach((pos, j) => {
                cube[pos] = values[i][j];
            });
        });

        return cube;
    }

    // Apply scramble with individual moves
    applyScramble(scrambleString) {
        console.log('Applying scramble:', scrambleString);
        const moves = scrambleString.split(' ').filter(move => move.trim());
        
        for (const move of moves) {
            const trimmedMove = move.trim();
            if (!trimmedMove) continue;
            
            const face = trimmedMove[0].toUpperCase();
            let clockwise = true;
            let double = false;
            
            if (trimmedMove.includes("'")) {
                clockwise = false;
            } else if (trimmedMove.includes('2')) {
                double = true;
            }
            
            if (double) {
                this.executeMove(face, true);
                this.executeMove(face, true);
            } else {
                this.executeMove(face, clockwise);
            }
        }
        
        return this.cubeString;
    }

    // Generate scramble
    generateScramble(moveCount = 20) {
        const faces = ['U', 'D', 'F', 'B', 'R', 'L'];
        const modifiers = ['', "'", '2'];
        const scramble = [];
        let lastFace = '';

        for (let i = 0; i < moveCount; i++) {
            let face;
            do {
                face = faces[Math.floor(Math.random() * faces.length)];
            } while (face === lastFace && faces.length > 1);
            
            const modifier = modifiers[Math.floor(Math.random() * modifiers.length)];
            scramble.push(face + modifier);
            lastFace = face;
        }

        return scramble.join(' ');
    }

    // Apply solution
    applySolution(solutionString) {
        return this.applyScramble(solutionString);
    }

    // Check if solved
    isSolved() {
        const stickersPerFace = this.size * this.size;
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        
        for (let faceIndex = 0; faceIndex < 6; faceIndex++) {
            const startPos = faceIndex * stickersPerFace;
            const firstSticker = this.cubeString[startPos];
            
            for (let i = 1; i < stickersPerFace; i++) {
                if (this.cubeString[startPos + i] !== firstSticker) {
                    return false;
                }
            }
        }
        
        return true;
    }

    // Convert to Kociemba format
    getKociembaString() {
        if (this.size !== 3) return null;

        // Simple mapping: W→U, Y→D, G→F, B→B, R→R, O→L
        const mapping = {
            'W': 'U', 'Y': 'D', 'G': 'F', 'B': 'B', 'R': 'R', 'O': 'L'
        };
        
        return this.cubeString.split('').map(c => mapping[c]).join('');
    }
}

// Test function
function testFinalImplementation() {
    console.log('=== Testing Final Implementation ===');
    
    const cube = new CubeStateFinal(3);
    const initial = cube.cubeString;
    
    console.log('Initial:', initial);
    
    // Test R R'
    cube.executeMove('R', true);
    cube.executeMove('R', false);
    
    if (cube.cubeString === initial) {
        console.log('✅ R R\' works correctly');
    } else {
        console.log('❌ R R\' failed');
        return false;
    }
    
    // Test all moves
    const moves = ['R', 'L', 'U', 'D', 'F', 'B'];
    for (const move of moves) {
        cube.resetToSolved();
        const before = cube.cubeString;
        
        cube.executeMove(move, true);
        cube.executeMove(move, false);
        
        if (cube.cubeString !== before) {
            console.log(`❌ ${move} ${move}' failed`);
            return false;
        }
    }
    
    console.log('✅ All moves work correctly');
    return true;
}

// Export
if (typeof window !== 'undefined') {
    window.CubeStateFinal = CubeStateFinal;
    window.testFinalImplementation = testFinalImplementation;
} else {
    module.exports = { CubeStateFinal, testFinalImplementation };
}

// Auto-test in Node.js
if (typeof window === 'undefined') {
    testFinalImplementation();
}
