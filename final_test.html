<!DOCTYPE html>
<html>
<head>
    <title>Final Cube Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { margin: 5px; padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🎲 Final Rubik's Cube Test Suite</h1>
    
    <div class="test-section">
        <h2>Step 1: Basic Move Validation</h2>
        <button onclick="testBasicMoves()">Test All Basic Moves</button>
        <div id="basic-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Step 2: Scramble Generation</h2>
        <button onclick="testScrambleGeneration()">Test Scramble Generation</button>
        <div id="scramble-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Step 3: Kociemba Conversion</h2>
        <button onclick="testKociembaConversion()">Test Kociemba Format</button>
        <div id="kociemba-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Step 4: Complete Solve Test</h2>
        <button onclick="testCompleteSolve()">Test Full Scramble & Solve</button>
        <div id="solve-results"></div>
    </div>

    <script src="js/cube-state.js"></script>
    <script>
        function log(elementId, message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            document.getElementById(elementId).appendChild(div);
        }

        function clearResults(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        function testBasicMoves() {
            const resultId = 'basic-results';
            clearResults(resultId);
            log(resultId, '🔧 Testing basic cube moves...', 'info');

            try {
                const cube = new CubeState(3);
                const moves = ['R', 'L', 'U', 'D', 'F', 'B'];
                let allPassed = true;

                for (const move of moves) {
                    const initial = cube.cubeString;
                    
                    // Apply move and its inverse
                    cube.executeMove(move, true);
                    cube.executeMove(move, false);
                    
                    if (cube.cubeString === initial) {
                        log(resultId, `✅ ${move} ${move}' works correctly`, 'success');
                    } else {
                        log(resultId, `❌ ${move} ${move}' failed`, 'error');
                        allPassed = false;
                    }
                    
                    cube.resetToSolved(); // Reset for next test
                }

                if (allPassed) {
                    log(resultId, '🎉 All basic moves work correctly!', 'success');
                } else {
                    log(resultId, '⚠️ Some basic moves failed', 'error');
                }

            } catch (error) {
                log(resultId, `❌ Error: ${error.message}`, 'error');
            }
        }

        function testScrambleGeneration() {
            const resultId = 'scramble-results';
            clearResults(resultId);
            log(resultId, '🎲 Testing scramble generation...', 'info');

            try {
                const cube = new CubeState(3);
                
                // Test scramble generation
                const scramble = cube.generateScramble(8);
                log(resultId, `Generated scramble: ${scramble}`, 'info');
                
                // Apply scramble
                const initialState = cube.cubeString;
                cube.applyScramble(scramble);
                const scrambledState = cube.cubeString;
                
                if (scrambledState !== initialState) {
                    log(resultId, '✅ Scramble successfully changed cube state', 'success');
                    log(resultId, `Initial:   ${initialState.substring(0, 20)}...`, 'info');
                    log(resultId, `Scrambled: ${scrambledState.substring(0, 20)}...`, 'info');
                } else {
                    log(resultId, '❌ Scramble did not change cube state', 'error');
                }

                // Test if cube is no longer solved
                if (!cube.isSolved()) {
                    log(resultId, '✅ Cube is properly scrambled (not solved)', 'success');
                } else {
                    log(resultId, '⚠️ Cube is still in solved state after scramble', 'warning');
                }

            } catch (error) {
                log(resultId, `❌ Error: ${error.message}`, 'error');
            }
        }

        function testKociembaConversion() {
            const resultId = 'kociemba-results';
            clearResults(resultId);
            log(resultId, '🔄 Testing Kociemba format conversion...', 'info');

            try {
                const cube = new CubeState(3);
                
                // Test with solved cube
                const solvedKociemba = cube.getKociembaString();
                if (solvedKociemba) {
                    log(resultId, `✅ Solved cube Kociemba: ${solvedKociemba.substring(0, 20)}...`, 'success');
                    log(resultId, `Length: ${solvedKociemba.length} (expected: 54)`, 'info');
                } else {
                    log(resultId, '❌ Failed to convert solved cube to Kociemba format', 'error');
                    return;
                }

                // Test with scrambled cube
                cube.applyScramble('R U F D');
                const scrambledKociemba = cube.getKociembaString();
                if (scrambledKociemba) {
                    log(resultId, `✅ Scrambled cube Kociemba: ${scrambledKociemba.substring(0, 20)}...`, 'success');
                    
                    // Verify it's different from solved
                    if (scrambledKociemba !== solvedKociemba) {
                        log(resultId, '✅ Scrambled Kociemba differs from solved', 'success');
                    } else {
                        log(resultId, '⚠️ Scrambled Kociemba same as solved', 'warning');
                    }
                } else {
                    log(resultId, '❌ Failed to convert scrambled cube to Kociemba format', 'error');
                }

            } catch (error) {
                log(resultId, `❌ Error: ${error.message}`, 'error');
            }
        }

        async function testCompleteSolve() {
            const resultId = 'solve-results';
            clearResults(resultId);
            log(resultId, '🧩 Testing complete scramble and solve process...', 'info');

            try {
                const cube = new CubeState(3);
                
                // Step 1: Generate and apply scramble
                const scramble = 'R U R\' U\''; // Simple known scramble
                log(resultId, `Using scramble: ${scramble}`, 'info');
                
                cube.applyScramble(scramble);
                log(resultId, '✅ Scramble applied', 'success');
                
                // Step 2: Convert to Kociemba
                const kociembaString = cube.getKociembaString();
                if (!kociembaString) {
                    log(resultId, '❌ Failed to convert to Kociemba format', 'error');
                    return;
                }
                
                log(resultId, `✅ Kociemba conversion successful: ${kociembaString.substring(0, 30)}...`, 'success');
                
                // Step 3: Send to solver
                log(resultId, '📡 Sending to solver...', 'info');
                
                const response = await fetch('http://localhost:5000/solve', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cube_string: kociembaString })
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    log(resultId, `❌ Server error ${response.status}: ${errorText}`, 'error');
                    return;
                }
                
                const data = await response.json();
                log(resultId, '✅ Server response received', 'success');
                
                if (data.error) {
                    log(resultId, `❌ Solver error: ${data.error}`, 'error');
                    return;
                }
                
                if (!data.solution) {
                    log(resultId, '❌ No solution provided', 'error');
                    return;
                }
                
                log(resultId, `✅ Solution received: ${data.solution}`, 'success');
                log(resultId, `Move count: ${data.move_count}`, 'info');
                
                // Step 4: Apply solution
                log(resultId, '🔧 Applying solution...', 'info');
                cube.applySolution(data.solution);
                
                // Step 5: Check if solved
                if (cube.isSolved()) {
                    log(resultId, '🎉 SUCCESS! Cube is solved!', 'success');
                } else {
                    log(resultId, '❌ Solution did not solve the cube', 'error');
                    log(resultId, `Final state: ${cube.cubeString.substring(0, 30)}...`, 'error');
                }

            } catch (error) {
                log(resultId, `❌ Error: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }
    </script>
</body>
</html>
