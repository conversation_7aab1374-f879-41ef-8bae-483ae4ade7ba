<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rubik's Cube Solver - Clean Implementation</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Rubik's Cube Solver</h1>
            <div class="cube-size-selector">
                <button class="size-btn" data-size="2">2x2</button>
                <button class="size-btn active" data-size="3">3x3</button>
                <button class="size-btn" data-size="4">4x4</button>
            </div>
        </header>

        <main>
            <div class="cube-container">
                <!-- 3D View -->
                <div class="view-section">
                    <h3>3D View</h3>
                    <canvas id="cube-canvas" width="400" height="400"></canvas>
                    
                    <!-- Cube Orientation Controls -->
                    <div class="orientation-controls">
                        <button class="orientation-btn" data-action="left">← Left</button>
                        <button class="orientation-btn" data-action="right">Right →</button>
                        <button class="orientation-btn" data-action="flip">↕ Flip</button>
                        <button class="orientation-btn" id="toggle-auto-rotate">🔄 Auto Rotate</button>
                    </div>
                </div>

                <!-- 2D View -->
                <div class="view-section">
                    <h3>2D View</h3>
                    <div class="flat-cube" id="flat-cube"></div>
                </div>
            </div>

            <!-- Face Controls -->
            <div class="controls-section">
                <h3>Face Rotations</h3>
                <div class="face-controls">
                    <div class="face-row">
                        <button class="face-btn" data-face="U" data-clockwise="true">U</button>
                        <button class="face-btn" data-face="U" data-clockwise="false">U'</button>
                    </div>
                    <div class="face-row">
                        <button class="face-btn" data-face="F" data-clockwise="true">F</button>
                        <button class="face-btn" data-face="F" data-clockwise="false">F'</button>
                        <button class="face-btn" data-face="B" data-clockwise="true">B</button>
                        <button class="face-btn" data-face="B" data-clockwise="false">B'</button>
                    </div>
                    <div class="face-row">
                        <button class="face-btn" data-face="L" data-clockwise="true">L</button>
                        <button class="face-btn" data-face="L" data-clockwise="false">L'</button>
                        <button class="face-btn" data-face="R" data-clockwise="true">R</button>
                        <button class="face-btn" data-face="R" data-clockwise="false">R'</button>
                    </div>
                    <div class="face-row">
                        <button class="face-btn" data-face="D" data-clockwise="true">D</button>
                        <button class="face-btn" data-face="D" data-clockwise="false">D'</button>
                    </div>
                </div>

                <!-- Wide Move Controls (for 4x4) -->
                <div class="wide-controls" id="wide-controls" style="display: none;">
                    <h4>Wide Moves (4x4)</h4>
                    <div class="wide-face-controls">
                        <div class="face-row">
                            <button class="wide-btn" data-face="Uw" data-clockwise="true">Uw</button>
                            <button class="wide-btn" data-face="Uw" data-clockwise="false">Uw'</button>
                        </div>
                        <div class="face-row">
                            <button class="wide-btn" data-face="Fw" data-clockwise="true">Fw</button>
                            <button class="wide-btn" data-face="Fw" data-clockwise="false">Fw'</button>
                            <button class="wide-btn" data-face="Bw" data-clockwise="true">Bw</button>
                            <button class="wide-btn" data-face="Bw" data-clockwise="false">Bw'</button>
                        </div>
                        <div class="face-row">
                            <button class="wide-btn" data-face="Lw" data-clockwise="true">Lw</button>
                            <button class="wide-btn" data-face="Lw" data-clockwise="false">Lw'</button>
                            <button class="wide-btn" data-face="Rw" data-clockwise="true">Rw</button>
                            <button class="wide-btn" data-face="Rw" data-clockwise="false">Rw'</button>
                        </div>
                        <div class="face-row">
                            <button class="wide-btn" data-face="Dw" data-clockwise="true">Dw</button>
                            <button class="wide-btn" data-face="Dw" data-clockwise="false">Dw'</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scramble and Solve -->
            <div class="action-controls">
                <button id="auto-scramble">🎲 Auto Scramble</button>
                <button id="solve-cube">🧩 Solve Cube</button>
                <button id="reset-cube">🔄 Reset</button>
            </div>

            <!-- Animation Speed Controls -->
            <div class="animation-controls">
                <h4>Scramble Animation Speed</h4>
                <div class="speed-controls">
                    <button class="speed-btn" data-speed="100">⚡ Fast (0.1s)</button>
                    <button class="speed-btn active" data-speed="400">🚶 Normal (0.4s)</button>
                    <button class="speed-btn" data-speed="800">🐌 Slow (0.8s)</button>
                </div>
            </div>

            <!-- Manual Scramble Input -->
            <div class="manual-scramble">
                <h4>Manual Scramble</h4>
                <div class="scramble-input-group">
                    <input type="text" id="manual-scramble-input" placeholder="Enter moves (e.g., R U R' U' R' F R2 U' R' U' R U R' F')" />
                    <button id="apply-manual-scramble">Apply Moves</button>
                </div>
                <div class="scramble-help">
                    <small>Use standard notation: R, L, U, D, F, B (add ' for counter-clockwise, 2 for double turns)</small>
                </div>
            </div>

            <!-- Status Display -->
            <div class="status-section">
                <div class="cube-state">
                    <h4>Cube State:</h4>
                    <div id="cube-string-display"></div>
                </div>
                <div class="solution-display">
                    <h4>Solution:</h4>
                    <div id="solution-moves"></div>
                </div>
            </div>

            <!-- Color Mappings Info -->
            <div class="info-panel">
                <h4>Face Color Mappings</h4>
                <div class="color-mappings">
                    <div class="mapping-item">
                        <span class="face-label">U (Up):</span>
                        <span class="color-box white"></span>
                        <span class="color-name">White</span>
                    </div>
                    <div class="mapping-item">
                        <span class="face-label">D (Down):</span>
                        <span class="color-box yellow"></span>
                        <span class="color-name">Yellow</span>
                    </div>
                    <div class="mapping-item">
                        <span class="face-label">F (Front):</span>
                        <span class="color-box green"></span>
                        <span class="color-name">Green</span>
                    </div>
                    <div class="mapping-item">
                        <span class="face-label">B (Back):</span>
                        <span class="color-box blue"></span>
                        <span class="color-name">Blue</span>
                    </div>
                    <div class="mapping-item">
                        <span class="face-label">R (Right):</span>
                        <span class="color-box red"></span>
                        <span class="color-name">Red</span>
                    </div>
                    <div class="mapping-item">
                        <span class="face-label">L (Left):</span>
                        <span class="color-box orange"></span>
                        <span class="color-name">Orange</span>
                    </div>
                </div>

                <div class="note">
                    <small><strong>Note:</strong> Face operations work with fixed color mappings independent of cube orientation. Visual rotation (Left/Right/Flip) only changes the view.</small>
                </div>

                <h4>Mouse Controls</h4>
                <div class="mouse-controls-info">
                    <div class="control-item">
                        <strong>Drag:</strong> Rotate cube view manually
                    </div>
                    <div class="control-item">
                        <strong>Double-click:</strong> Toggle auto-rotation
                    </div>
                    <div class="control-item">
                        <strong>Auto-rotation:</strong> Pauses when dragging, resumes after 2 seconds
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <!-- Kociemba Algorithm Components -->
    <script src="js/kociemba-enums.js"></script>
    <script src="js/kociemba-face.js"></script>
    <script src="js/kociemba-solver.js"></script>

    <!-- Core Cube Components -->
    <script src="js/cube-state.js"></script>
    <script src="js/cube-3d.js"></script>
    <script src="js/cube-2d.js"></script>
    <script src="js/controls.js"></script>
    <script src="js/solver.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
