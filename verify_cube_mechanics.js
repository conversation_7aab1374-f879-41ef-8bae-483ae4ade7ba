// Standalone test script to verify cube mechanics
// Run this in Node.js or browser console

// Simplified CubeState class for testing
class TestCubeState {
    constructor(size = 3) {
        this.size = size;
        this.initializeState();
    }

    initializeState() {
        this.colors = {
            'U': 'white',   'D': 'yellow',  'F': 'green',
            'B': 'blue',    'R': 'red',     'L': 'orange'
        };

        this.colorToChar = {
            'white': 'W',   'yellow': 'Y',  'green': 'G',
            'blue': 'B',    'red': 'R',     'orange': 'O'
        };

        this.charToColor = {
            'W': 'white',   'Y': 'yellow',  'G': 'green',
            'B': 'blue',    'R': 'red',     'O': 'orange'
        };

        this.resetToSolved();
    }

    resetToSolved() {
        const stickersPerFace = this.size * this.size;
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        
        this.cubeString = '';
        faceOrder.forEach(face => {
            const color = this.colors[face];
            const char = this.colorToChar[color];
            this.cubeString += char.repeat(stickersPerFace);
        });
    }

    stringToFaces() {
        const faces = {};
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        const stickersPerFace = this.size * this.size;

        faceOrder.forEach((face, faceIndex) => {
            faces[face] = [];
            for (let row = 0; row < this.size; row++) {
                faces[face][row] = [];
                for (let col = 0; col < this.size; col++) {
                    const stringIndex = faceIndex * stickersPerFace + row * this.size + col;
                    const char = this.cubeString[stringIndex];
                    const color = this.charToColor[char] || 'gray';
                    faces[face][row][col] = color;
                }
            }
        });

        return faces;
    }

    facesToString(faces) {
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        let result = '';

        faceOrder.forEach(face => {
            for (let row = 0; row < this.size; row++) {
                for (let col = 0; col < this.size; col++) {
                    const color = faces[face][row][col];
                    const char = this.colorToChar[color] || 'W';
                    result += char;
                }
            }
        });

        return result;
    }

    rotateFaceArray(faceArray, clockwise = true) {
        const size = this.size;
        const temp = faceArray.map(row => [...row]);
        const centerValue = (size === 3) ? temp[1][1] : null;

        if (clockwise) {
            for (let i = 0; i < size; i++) {
                for (let j = 0; j < size; j++) {
                    if (size === 3 && i === 1 && j === 1) continue;
                    faceArray[j][size - 1 - i] = temp[i][j];
                }
            }
        } else {
            for (let i = 0; i < size; i++) {
                for (let j = 0; j < size; j++) {
                    if (size === 3 && i === 1 && j === 1) continue;
                    faceArray[size - 1 - j][i] = temp[i][j];
                }
            }
        }

        if (size === 3) {
            faceArray[1][1] = centerValue;
        }
    }

    getColumn(faceArray, colIndex) {
        return faceArray.map(row => row[colIndex]);
    }

    setColumn(faceArray, colIndex, values) {
        for (let i = 0; i < faceArray.length; i++) {
            faceArray[i][colIndex] = values[i];
        }
    }

    executeMove(face, clockwise = true) {
        const faces = this.stringToFaces();
        
        // Rotate the face itself
        this.rotateFaceArray(faces[face], clockwise);
        
        // Update adjacent faces
        this.updateAdjacentFaces(faces, face, clockwise);
        
        // Convert back to string
        this.cubeString = this.facesToString(faces);
        
        return this.cubeString;
    }

    updateAdjacentFaces(faces, face, clockwise) {
        const size = this.size;

        const adjacentMappings = {
            'U': {
                cycle: [
                    { face: 'F', row: 0 },
                    { face: 'L', row: 0 },
                    { face: 'B', row: 0 },
                    { face: 'R', row: 0 }
                ]
            },
            'D': {
                cycle: [
                    { face: 'F', row: size-1 },
                    { face: 'R', row: size-1 },
                    { face: 'B', row: size-1 },
                    { face: 'L', row: size-1 }
                ]
            },
            'F': {
                cycle: [
                    { face: 'U', row: size-1, type: 'row' },
                    { face: 'R', col: 0, type: 'col' },
                    { face: 'D', row: 0, type: 'row', reverse: true },
                    { face: 'L', col: size-1, type: 'col', reverse: true }
                ]
            },
            'B': {
                cycle: [
                    { face: 'U', row: 0, type: 'row', reverse: true },
                    { face: 'L', col: 0, type: 'col' },
                    { face: 'D', row: size-1, type: 'row' },
                    { face: 'R', col: size-1, type: 'col', reverse: true }
                ]
            },
            'R': {
                cycle: [
                    { face: 'U', col: size-1, type: 'col' },
                    { face: 'B', col: 0, type: 'col', reverse: true },
                    { face: 'D', col: size-1, type: 'col' },
                    { face: 'F', col: size-1, type: 'col' }
                ]
            },
            'L': {
                cycle: [
                    { face: 'U', col: 0, type: 'col' },
                    { face: 'F', col: 0, type: 'col' },
                    { face: 'D', col: 0, type: 'col' },
                    { face: 'B', col: size-1, type: 'col', reverse: true }
                ]
            }
        };

        const mapping = adjacentMappings[face];
        if (!mapping) return;

        const values = mapping.cycle.map(item => {
            let val;
            if (item.type === 'col' || item.col !== undefined) {
                val = this.getColumn(faces[item.face], item.col).slice();
            } else {
                val = faces[item.face][item.row].slice();
            }
            return item.reverse ? val.reverse() : val;
        });

        if (clockwise) {
            // Clockwise: move values forward in the cycle (0→1, 1→2, 2→3, 3→0)
            const temp = values[0];
            for (let i = 0; i < values.length - 1; i++) {
                values[i] = values[i + 1];
            }
            values[values.length - 1] = temp;
        } else {
            // Counterclockwise: move values backward in the cycle (0→3, 1→0, 2→1, 3→2)
            const temp = values[values.length - 1];
            for (let i = values.length - 1; i > 0; i--) {
                values[i] = values[i - 1];
            }
            values[0] = temp;
        }

        mapping.cycle.forEach((item, i) => {
            let val = item.reverse ? values[i].slice().reverse() : values[i].slice();
            
            if (item.type === 'col' || item.col !== undefined) {
                this.setColumn(faces[item.face], item.col, val);
            } else {
                faces[item.face][item.row] = val;
            }
        });
    }

    isSolved() {
        const faces = this.stringToFaces();
        for (const face of ['U', 'R', 'F', 'D', 'L', 'B']) {
            const faceArray = faces[face];
            const firstColor = faceArray[0][0];
            for (let row = 0; row < this.size; row++) {
                for (let col = 0; col < this.size; col++) {
                    if (faceArray[row][col] !== firstColor) {
                        return false;
                    }
                }
            }
        }
        return true;
    }
}

// Test functions
function testBasicMoves() {
    console.log('=== Testing Basic Moves ===');
    
    const cube = new TestCubeState(3);
    const initial = cube.cubeString;
    
    console.log('Initial state:', initial.substring(0, 20) + '...');
    
    // Test R U R' U'
    cube.executeMove('R', true);
    console.log('After R:', cube.cubeString.substring(0, 20) + '...');
    
    cube.executeMove('U', true);
    console.log('After U:', cube.cubeString.substring(0, 20) + '...');
    
    cube.executeMove('R', false);
    console.log('After R\':', cube.cubeString.substring(0, 20) + '...');
    
    cube.executeMove('U', false);
    console.log('After U\':', cube.cubeString.substring(0, 20) + '...');
    
    const final = cube.cubeString;
    console.log('Final state:', final.substring(0, 20) + '...');
    
    if (final === initial) {
        console.log('✓ SUCCESS: R U R\' U\' returned to solved state!');
        return true;
    } else {
        console.log('✗ FAILED: R U R\' U\' did not return to solved state');
        console.log('Expected:', initial);
        console.log('Got:     ', final);
        return false;
    }
}

function testAllBasicMoves() {
    console.log('\n=== Testing All Basic Moves ===');
    
    const moves = ['R', 'L', 'U', 'D', 'F', 'B'];
    let allPassed = true;
    
    for (const move of moves) {
        const cube = new TestCubeState(3);
        const initial = cube.cubeString;
        
        // Do move then undo it
        cube.executeMove(move, true);
        cube.executeMove(move, false);
        
        if (cube.cubeString === initial) {
            console.log(`✓ ${move} ${move}' sequence works correctly`);
        } else {
            console.log(`✗ ${move} ${move}' sequence failed`);
            allPassed = false;
        }
    }
    
    return allPassed;
}

// Run tests
if (typeof window === 'undefined') {
    // Node.js environment
    const test1 = testBasicMoves();
    const test2 = testAllBasicMoves();
    
    console.log('\n=== FINAL RESULTS ===');
    console.log('Basic R U R\' U\' test:', test1 ? 'PASSED' : 'FAILED');
    console.log('All basic moves test:', test2 ? 'PASSED' : 'FAILED');
    console.log('Overall:', (test1 && test2) ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED');
} else {
    // Browser environment
    window.TestCubeState = TestCubeState;
    window.testBasicMoves = testBasicMoves;
    window.testAllBasicMoves = testAllBasicMoves;
}
